<!DOCTYPE html>
<html>
<head>
    <title>Test AJAX Session</title>
</head>
<body>
    <h1>Test AJAX Session</h1>
    
    <button onclick="testSession()">Test Session</button>
    <button onclick="testEventCreation()">Test Event Creation</button>
    
    <div id="results"></div>
    
    <script>
    function testSession() {
        fetch('test_session.php', {
            method: 'POST',
            credentials: 'same-origin',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'test=1&status=published&is_recurring=on'
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('results').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        })
        .catch(error => {
            document.getElementById('results').innerHTML = '<div style="color: red;">Error: ' + error.message + '</div>';
        });
    }
    
    function testEventCreation() {
        const formData = new FormData();
        formData.append('title', 'Test Event');
        formData.append('start_datetime', '2025-01-01T10:00');
        formData.append('end_datetime', '2025-01-01T11:00');
        formData.append('location', 'Test Location');
        formData.append('status', 'published');
        formData.append('is_recurring', 'on');
        formData.append('recurrence_type', 'weekly');
        formData.append('recurrence_interval', '1');
        formData.append('recurrence_count', '5');
        
        fetch('create_event_with_materials.php', {
            method: 'POST',
            credentials: 'same-origin',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('results').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        })
        .catch(error => {
            document.getElementById('results').innerHTML = '<div style="color: red;">Error: ' + error.message + '</div>';
        });
    }
    </script>
</body>
</html>
