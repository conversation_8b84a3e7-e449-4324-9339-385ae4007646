<?php
require_once 'config.php';

echo "<h1>🔍 Debug File Paths</h1>\n";

// Check event files for event 35
$stmt = $pdo->prepare("SELECT id, event_id, file_name, file_path, file_category FROM event_files WHERE event_id = 35");
$stmt->execute();
$files = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h2>📁 Files for Event 35:</h2>\n";
if (empty($files)) {
    echo "<p>No files found for event 35</p>\n";
} else {
    echo "<table border='1' cellpadding='5'>\n";
    echo "<tr><th>ID</th><th>File Name</th><th>File Path</th><th>Category</th><th>File Exists?</th><th>Correct Path</th></tr>\n";
    
    foreach ($files as $file) {
        $filePath = $file['file_path'];
        $fileExists = file_exists($filePath);
        
        // Try different path variations
        $correctPath = '';
        $possiblePaths = [
            $filePath,
            'uploads/' . basename($filePath),
            'uploads/events/' . basename($filePath),
            '../uploads/events/' . basename($filePath)
        ];
        
        foreach ($possiblePaths as $testPath) {
            if (file_exists($testPath)) {
                $correctPath = $testPath;
                break;
            }
        }
        
        echo "<tr>";
        echo "<td>{$file['id']}</td>";
        echo "<td>{$file['file_name']}</td>";
        echo "<td>{$file['file_path']}</td>";
        echo "<td>{$file['file_category']}</td>";
        echo "<td>" . ($fileExists ? '✅' : '❌') . "</td>";
        echo "<td>{$correctPath}</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
}

// Check the actual files in the uploads directory
echo "<h2>📂 Actual Files in uploads/events/:</h2>\n";
$uploadsDir = 'uploads/events/';
if (is_dir($uploadsDir)) {
    $actualFiles = scandir($uploadsDir);
    echo "<ul>\n";
    foreach ($actualFiles as $file) {
        if ($file !== '.' && $file !== '..' && !is_dir($uploadsDir . $file)) {
            echo "<li>$file</li>\n";
        }
    }
    echo "</ul>\n";
} else {
    echo "<p>Directory uploads/events/ not found</p>\n";
}

// Check current working directory
echo "<h2>📍 Current Directory Info:</h2>\n";
echo "<p>Current working directory: " . getcwd() . "</p>\n";
echo "<p>Script directory: " . __DIR__ . "</p>\n";

// Test file access
echo "<h2>🧪 File Access Test:</h2>\n";
$testFile = 'uploads/events/event_35_1752636150_68771af633673.pdf';
echo "<p>Testing file: $testFile</p>\n";
echo "<p>File exists: " . (file_exists($testFile) ? '✅ Yes' : '❌ No') . "</p>\n";
if (file_exists($testFile)) {
    echo "<p>File size: " . filesize($testFile) . " bytes</p>\n";
    echo "<p>File is readable: " . (is_readable($testFile) ? '✅ Yes' : '❌ No') . "</p>\n";
}

// Test URL construction
echo "<h2>🌐 URL Construction Test:</h2>\n";
$baseUrl = 'http://localhost/campaign/church/';
$fileUrl = $baseUrl . $testFile;
echo "<p>Constructed URL: <a href='$fileUrl' target='_blank'>$fileUrl</a></p>\n";

?>
