<?php
require_once 'church/config.php';

echo "<h1>Debug Admin Banner Paths</h1>";

// Get events with banners
$stmt = $pdo->query("
    SELECT e.id, e.title, ef.file_path, ef.file_type, ef.file_name
    FROM events e
    LEFT JOIN event_files ef ON e.id = ef.event_id AND ef.is_header_banner = 1
    WHERE ef.file_path IS NOT NULL
    ORDER BY e.id
");
$events = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h2>Events with Banners:</h2>";
echo "<table border='1'>";
echo "<tr><th>Event ID</th><th>Title</th><th>File Path</th><th>File Exists?</th><th>Thumbnail Path</th><th>Thumbnail Exists?</th></tr>";

foreach ($events as $event) {
    $file_path = $event['file_path'];
    $file_exists = file_exists($file_path) ? 'YES' : 'NO';

    // Test admin path resolution (from admin directory)
    $admin_banner_path = 'church/' . $file_path;
    $admin_file_exists = file_exists($admin_banner_path) ? 'YES' : 'NO';

    // Generate thumbnail path like in admin
    $thumbnail_path = str_replace('/promotional/', '/thumbnails/', dirname($file_path)) . '/thumb_' . basename($file_path);
    $thumbnail_exists = file_exists($thumbnail_path) ? 'YES' : 'NO';

    echo "<tr>";
    echo "<td>{$event['id']}</td>";
    echo "<td>{$event['title']}</td>";
    echo "<td>{$file_path}</td>";
    echo "<td>{$file_exists}</td>";
    echo "<td>{$admin_banner_path}</td>";
    echo "<td>{$admin_file_exists}</td>";
    echo "<td>{$thumbnail_path}</td>";
    echo "<td>{$thumbnail_exists}</td>";
    echo "</tr>";
}
echo "</table>";

// Check if uploads directory exists
echo "<h2>Directory Check:</h2>";
$upload_dirs = [
    'church/uploads/events/promotional',
    'church/uploads/events/thumbnails'
];

foreach ($upload_dirs as $dir) {
    $exists = is_dir($dir) ? 'EXISTS' : 'MISSING';
    echo "<p>$dir: $exists</p>";
}
?>
