<?php
session_start();
require_once 'includes/config.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    echo json_encode(['success' => false, 'message' => 'Not logged in']);
    exit();
}

// Handle different actions
if (isset($_POST['action']) && $_POST['action'] === 'get_recent_events') {
    try {
        $stmt = $conn->prepare("
            SELECT id, title, status, created_at, event_date, location
            FROM events
            WHERE created_by = ?
            ORDER BY created_at DESC
            LIMIT 10
        ");
        $stmt->execute([$_SESSION['admin_id']]);
        $events = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'events' => $events,
            'count' => count($events)
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Error loading events: ' . $e->getMessage()
        ]);
    }
    exit();
}

// Default session test response
echo json_encode([
    'success' => true,
    'session_data' => [
        'admin_id' => $_SESSION['admin_id'] ?? 'not set',
        'admin_name' => $_SESSION['admin_name'] ?? 'not set',
        'session_id' => session_id()
    ],
    'post_data' => $_POST,
    'request_method' => $_SERVER['REQUEST_METHOD'],
    'timestamp' => date('Y-m-d H:i:s')
]);
?>
