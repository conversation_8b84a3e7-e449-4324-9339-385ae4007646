<?php
// Include AJAX session handler for proper session management
require_once 'includes/ajax-session-handler.php';

// Validate admin session
$sessionValidation = handleAjaxSessionValidation();

// Include configuration safely
$pdo = includeConfigForAjax();

sendAjaxResponse([
    'success' => true,
    'session_data' => [
        'admin_id' => $_SESSION['admin_id'] ?? 'not set',
        'admin_name' => $_SESSION['admin_name'] ?? 'not set',
        'session_id' => session_id()
    ],
    'post_data' => $_POST,
    'request_method' => $_SERVER['REQUEST_METHOD'],
    'timestamp' => date('Y-m-d H:i:s'),
    'session_validation' => $sessionValidation
]);
?>
