<?php
session_start();
require_once '../config.php';

header('Content-Type: application/json');

echo json_encode([
    'success' => true,
    'session_data' => [
        'admin_id' => $_SESSION['admin_id'] ?? 'not set',
        'admin_name' => $_SESSION['admin_name'] ?? 'not set',
        'session_id' => session_id()
    ],
    'post_data' => $_POST,
    'request_method' => $_SERVER['REQUEST_METHOD'],
    'timestamp' => date('Y-m-d H:i:s')
]);
?>
