<?php
/**
 * AJAX Session Handler
 * 
 * Unified session management for AJAX requests to prevent session conflicts
 * and timeout issues that require logout/login cycles.
 */

// Prevent any output before we start
ob_start();

// Set error handling for AJAX requests
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Register shutdown function to catch fatal errors and return proper JSON
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        // Clean any output
        while (ob_get_level()) {
            ob_end_clean();
        }
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Server error occurred',
            'error' => $error['message'],
            'debug' => [
                'file' => basename($error['file']),
                'line' => $error['line']
            ]
        ]);
        exit;
    }
});

/**
 * Initialize AJAX session with proper timeout handling
 */
function initAjaxSession() {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        // Configure session for AJAX requests
        ini_set('session.cookie_lifetime', 0);
        ini_set('session.gc_maxlifetime', 7200); // 2 hours
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', 0);
        ini_set('session.use_strict_mode', 0);
        
        session_start();
    }
    
    // Initialize session timestamps if not set
    if (!isset($_SESSION['CREATED'])) {
        $_SESSION['CREATED'] = time();
    }
    
    // Update last activity - this is crucial for preventing timeouts
    $_SESSION['LAST_ACTIVITY'] = time();
    
    return true;
}

/**
 * Check if admin session is valid for AJAX requests
 */
function validateAjaxAdminSession() {
    // Initialize session first
    initAjaxSession();
    
    // Check if admin is logged in
    if (!isset($_SESSION['admin_id']) || empty($_SESSION['admin_id'])) {
        return [
            'valid' => false,
            'error' => 'not_logged_in',
            'message' => 'Admin session not found. Please login again.'
        ];
    }
    
    // Check session timeout (2 hours = 7200 seconds)
    $timeout_seconds = 7200;
    if (isset($_SESSION['LAST_ACTIVITY'])) {
        $inactive_time = time() - $_SESSION['LAST_ACTIVITY'];
        if ($inactive_time >= $timeout_seconds) {
            // Session timed out for AJAX: do NOT destroy session, just return error
            return [
                'valid' => false,
                'error' => 'session_timeout',
                'message' => 'Session has expired due to inactivity. Please login again.'
            ];
        }
    }
    
    // Update activity timestamp
    $_SESSION['LAST_ACTIVITY'] = time();
    
    return [
        'valid' => true,
        'admin_id' => $_SESSION['admin_id'],
        'admin_name' => $_SESSION['admin_name'] ?? 'Unknown',
        'remaining_time' => $timeout_seconds - (time() - $_SESSION['LAST_ACTIVITY'])
    ];
}

/**
 * Send JSON response and exit
 */
function sendAjaxResponse($data) {
    // Clean any output that might have been generated
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    // Set proper headers
    header('Content-Type: application/json');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
    
    // Send response
    echo json_encode($data);
    exit;
}

/**
 * Handle AJAX session validation and return appropriate response
 */
function handleAjaxSessionValidation() {
    $validation = validateAjaxAdminSession();
    
    if (!$validation['valid']) {
        sendAjaxResponse([
            'success' => false,
            'session_valid' => false,
            'error' => $validation['error'],
            'message' => $validation['message']
        ]);
    }
    
    return $validation;
}

/**
 * Include config safely for AJAX requests
 */
function includeConfigForAjax() {
    // Include config without session manager conflicts
    if (!defined('CONFIG_INCLUDED')) {
        require_once __DIR__ . '/../../config.php';
    }
    
    // Ensure PDO connection is available
    global $pdo;
    if (!isset($pdo) || !($pdo instanceof PDO)) {
        sendAjaxResponse([
            'success' => false,
            'message' => 'Database connection not available'
        ]);
    }
    
    return $pdo;
}

// Auto-initialize session when this file is included
initAjaxSession();
