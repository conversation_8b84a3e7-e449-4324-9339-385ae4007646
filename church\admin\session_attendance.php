<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection
$conn = $pdo;

$message = '';
$error = '';

// Get session ID from URL
$session_id = isset($_GET['session_id']) ? (int)$_GET['session_id'] : 0;

if ($session_id <= 0) {
    header("Location: events.php");
    exit();
}

// Get session and event details
try {
    $stmt = $conn->prepare("
        SELECT es.*, e.title as event_title, e.id as event_id
        FROM event_sessions es
        JOIN events e ON es.event_id = e.id
        WHERE es.id = ?
    ");
    $stmt->execute([$session_id]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        header("Location: events.php");
        exit();
    }
} catch (PDOException $e) {
    $error = "Error loading session: " . $e->getMessage();
    $session = null;
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'add_registration') {
        $member_id = !empty($_POST['member_id']) ? (int)$_POST['member_id'] : null;
        $guest_name = trim($_POST['guest_name']);
        $guest_email = trim($_POST['guest_email']);

        if (!$member_id && (empty($guest_name) || empty($guest_email))) {
            $error = "Please select a member or provide guest details.";
        } else {
            try {
                $stmt = $conn->prepare("
                    INSERT INTO session_attendance (session_id, member_id, guest_name, guest_email, attendance_status)
                    VALUES (?, ?, ?, ?, 'registered')
                ");
                $stmt->execute([$session_id, $member_id, $guest_name ?: null, $guest_email ?: null]);
                
                $message = "Registration added successfully!";
                
                // Clear form data by redirecting
                header("Location: session_attendance.php?session_id=" . $session_id . "&added=1");
                exit();
                
            } catch (PDOException $e) {
                if ($e->getCode() == 23000) { // Duplicate entry
                    $error = "This person is already registered for this session.";
                } else {
                    $error = "Error adding registration: " . $e->getMessage();
                }
            }
        }
    } elseif ($_POST['action'] === 'update_attendance') {
        $attendance_id = (int)$_POST['attendance_id'];
        $attendance_status = $_POST['attendance_status'];
        $notes = trim($_POST['notes']);

        try {
            $stmt = $conn->prepare("
                UPDATE session_attendance SET
                    attendance_status = ?, notes = ?,
                    attendance_date = CASE WHEN ? = 'attended' THEN NOW() ELSE attendance_date END
                WHERE id = ? AND session_id = ?
            ");
            $stmt->execute([$attendance_status, $notes, $attendance_status, $attendance_id, $session_id]);
            
            $message = "Attendance updated successfully!";
            
            // Clear form data by redirecting
            header("Location: session_attendance.php?session_id=" . $session_id . "&updated=1");
            exit();
            
        } catch (PDOException $e) {
            $error = "Error updating attendance: " . $e->getMessage();
        }
    } elseif ($_POST['action'] === 'delete_registration') {
        $attendance_id = (int)$_POST['attendance_id'];
        
        try {
            $stmt = $conn->prepare("DELETE FROM session_attendance WHERE id = ? AND session_id = ?");
            $stmt->execute([$attendance_id, $session_id]);
            
            $message = "Registration removed successfully!";
            
            // Clear form data by redirecting
            header("Location: session_attendance.php?session_id=" . $session_id . "&deleted=1");
            exit();
            
        } catch (PDOException $e) {
            $error = "Error removing registration: " . $e->getMessage();
        }
    }
}

// Check for success messages from redirect
if (isset($_GET['added']) && $_GET['added'] == '1') {
    $message = "Registration added successfully!";
} elseif (isset($_GET['updated']) && $_GET['updated'] == '1') {
    $message = "Attendance updated successfully!";
} elseif (isset($_GET['deleted']) && $_GET['deleted'] == '1') {
    $message = "Registration removed successfully!";
}

// Get attendance records for this session
$attendance_records = [];
try {
    $stmt = $conn->prepare("
        SELECT sa.*, m.full_name as member_name, m.email as member_email
        FROM session_attendance sa
        LEFT JOIN members m ON sa.member_id = m.id
        WHERE sa.session_id = ?
        ORDER BY sa.registration_date ASC
    ");
    $stmt->execute([$session_id]);
    $attendance_records = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error loading attendance records: " . $e->getMessage());
}

// Get members for dropdown
$members = [];
try {
    $stmt = $conn->prepare("SELECT id, full_name, email FROM members WHERE status = 'active' ORDER BY full_name ASC");
    $stmt->execute();
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error loading members: " . $e->getMessage());
}

// Calculate statistics
$total_registered = count($attendance_records);
$total_attended = count(array_filter($attendance_records, function($record) {
    return $record['attendance_status'] === 'attended';
}));
$total_no_show = count(array_filter($attendance_records, function($record) {
    return $record['attendance_status'] === 'no_show';
}));

// Page title and header info
$page_title = 'Session Attendance - ' . ($session ? htmlspecialchars($session['session_title']) : 'Unknown Session');
$page_header = 'Session Attendance';
$page_description = 'Manage attendance for: ' . ($session ? htmlspecialchars($session['session_title']) : 'Unknown Session');

// Include header
include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-people"></i> Session Attendance
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="event_sessions.php?event_id=<?php echo $session['event_id']; ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Sessions
        </a>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($session): ?>
<!-- Session Information -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-info-circle"></i> Session Information
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <h4><?php echo htmlspecialchars($session['session_title']); ?></h4>
                <p class="text-muted mb-1"><strong>Event:</strong> <?php echo htmlspecialchars($session['event_title']); ?></p>
                <?php if (!empty($session['session_description'])): ?>
                    <p class="text-muted"><?php echo htmlspecialchars($session['session_description']); ?></p>
                <?php endif; ?>
            </div>
            <div class="col-md-4">
                <p><strong>Date:</strong> <?php echo date('M j, Y', strtotime($session['start_datetime'])); ?></p>
                <p><strong>Time:</strong> <?php echo date('g:i A', strtotime($session['start_datetime'])); ?> - <?php echo date('g:i A', strtotime($session['end_datetime'])); ?></p>
                <p><strong>Location:</strong> <?php echo htmlspecialchars($session['location'] ?: 'Not specified'); ?></p>
                <p><strong>Instructor:</strong> <?php echo htmlspecialchars($session['instructor_name'] ?: 'Not specified'); ?></p>
                <p><strong>Capacity:</strong> <?php echo $session['max_attendees'] ? number_format($session['max_attendees']) : 'Unlimited'; ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Attendance Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary"><?php echo $total_registered; ?></h3>
                <p class="card-text">Total Registered</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-success"><?php echo $total_attended; ?></h3>
                <p class="card-text">Attended</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-warning"><?php echo $total_no_show; ?></h3>
                <p class="card-text">No Show</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-info"><?php echo $total_registered > 0 ? round(($total_attended / $total_registered) * 100) : 0; ?>%</h3>
                <p class="card-text">Attendance Rate</p>
            </div>
        </div>
    </div>
</div>

<!-- Attendance Management -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-people"></i> Attendance Records
        </h5>
        <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addRegistrationModal">
            <i class="bi bi-plus-circle"></i> Add Registration
        </button>
    </div>
    <div class="card-body">
        <?php if (empty($attendance_records)): ?>
            <div class="text-center py-5">
                <i class="bi bi-people display-1 text-muted"></i>
                <h4 class="mt-3">No Registrations Yet</h4>
                <p class="text-muted">This session doesn't have any registrations yet.</p>
                <p class="text-muted">Click "Add Registration" to register the first attendee.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Attendee</th>
                            <th>Email</th>
                            <th>Registration Date</th>
                            <th>Attendance Status</th>
                            <th>Attendance Date</th>
                            <th>Notes</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($attendance_records as $record): ?>
                            <tr>
                                <td>
                                    <?php if ($record['member_id']): ?>
                                        <i class="bi bi-person-badge text-primary"></i> <?php echo htmlspecialchars($record['member_name'] ?: 'Unknown Member'); ?>
                                        <small class="text-muted d-block">Member</small>
                                    <?php else: ?>
                                        <i class="bi bi-person text-secondary"></i> <?php echo htmlspecialchars($record['guest_name'] ?: 'Unknown Guest'); ?>
                                        <small class="text-muted d-block">Guest</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php echo htmlspecialchars($record['member_email'] ?: $record['guest_email'] ?: 'No email provided'); ?>
                                </td>
                                <td>
                                    <?php echo date('M j, Y g:i A', strtotime($record['registration_date'])); ?>
                                </td>
                                <td>
                                    <?php
                                    $status_class = '';
                                    $status_icon = '';
                                    switch ($record['attendance_status']) {
                                        case 'registered':
                                            $status_class = 'bg-info';
                                            $status_icon = 'bi-clock';
                                            break;
                                        case 'attended':
                                            $status_class = 'bg-success';
                                            $status_icon = 'bi-check-circle';
                                            break;
                                        case 'no_show':
                                            $status_class = 'bg-warning';
                                            $status_icon = 'bi-x-circle';
                                            break;
                                    }
                                    ?>
                                    <span class="badge <?php echo $status_class; ?>">
                                        <i class="bi <?php echo $status_icon; ?>"></i> <?php echo ucfirst(str_replace('_', ' ', $record['attendance_status'])); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php echo $record['attendance_date'] ? date('M j, Y g:i A', strtotime($record['attendance_date'])) : '-'; ?>
                                </td>
                                <td>
                                    <?php echo htmlspecialchars($record['notes'] ?: '-'); ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary" onclick="editAttendance(<?php echo $record['id']; ?>)" title="Update Attendance">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" onclick="deleteRegistration(<?php echo $record['id']; ?>, '<?php echo htmlspecialchars($record['member_name'] ?: $record['guest_name']); ?>')" title="Remove Registration">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php else: ?>
<div class="alert alert-warning">
    <i class="bi bi-exclamation-triangle"></i> Session not found or could not be loaded.
</div>
<?php endif; ?>

<!-- Add Registration Modal -->
<div class="modal fade" id="addRegistrationModal" tabindex="-1" aria-labelledby="addRegistrationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addRegistrationModalLabel">
                    <i class="bi bi-plus-circle"></i> Add Registration
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addRegistrationForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_registration">
                    <input type="hidden" name="session_id" value="<?php echo htmlspecialchars($session_id); ?>">

                    <div class="mb-3">
                        <label class="form-label">Registration Type</label>
                        <div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="registration_type" id="member_registration" value="member" checked onchange="toggleRegistrationType()">
                                <label class="form-check-label" for="member_registration">
                                    Existing Member
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="registration_type" id="guest_registration" value="guest" onchange="toggleRegistrationType()">
                                <label class="form-check-label" for="guest_registration">
                                    Guest
                                </label>
                            </div>
                        </div>
                    </div>

                    <div id="member_fields">
                        <div class="mb-3">
                            <label for="member_id" class="form-label">Select Member</label>
                            <select class="form-select" id="member_id" name="member_id">
                                <option value="">Choose a member...</option>
                                <?php foreach ($members as $member): ?>
                                    <option value="<?php echo $member['id']; ?>"><?php echo htmlspecialchars($member['full_name']); ?> (<?php echo htmlspecialchars($member['email']); ?>)</option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div id="guest_fields" style="display: none;">
                        <div class="mb-3">
                            <label for="guest_name" class="form-label">Guest Name</label>
                            <input type="text" class="form-control" id="guest_name" name="guest_name">
                        </div>
                        <div class="mb-3">
                            <label for="guest_email" class="form-label">Guest Email</label>
                            <input type="email" class="form-control" id="guest_email" name="guest_email">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Add Registration
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Attendance Modal -->
<div class="modal fade" id="editAttendanceModal" tabindex="-1" aria-labelledby="editAttendanceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editAttendanceModalLabel">
                    <i class="bi bi-pencil"></i> Update Attendance
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editAttendanceForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_attendance">
                    <input type="hidden" name="session_id" value="<?php echo htmlspecialchars($session_id); ?>">
                    <input type="hidden" name="attendance_id" id="edit_attendance_id">

                    <div class="mb-3">
                        <label class="form-label">Attendee</label>
                        <p id="edit_attendee_name" class="form-control-plaintext"></p>
                    </div>

                    <div class="mb-3">
                        <label for="edit_attendance_status" class="form-label">Attendance Status</label>
                        <select class="form-select" id="edit_attendance_status" name="attendance_status" required>
                            <option value="registered">Registered</option>
                            <option value="attended">Attended</option>
                            <option value="no_show">No Show</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="edit_notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="edit_notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle"></i> Update Attendance
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Registration Modal -->
<div class="modal fade" id="deleteRegistrationModal" tabindex="-1" aria-labelledby="deleteRegistrationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteRegistrationModalLabel">
                    <i class="bi bi-exclamation-triangle text-danger"></i> Remove Registration
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="deleteRegistrationForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="delete_registration">
                    <input type="hidden" name="session_id" value="<?php echo htmlspecialchars($session_id); ?>">
                    <input type="hidden" name="attendance_id" id="delete_attendance_id">

                    <p>Are you sure you want to remove the registration for "<strong id="delete_attendee_name"></strong>"?</p>
                    <p class="text-danger"><i class="bi bi-exclamation-triangle"></i> This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash"></i> Remove Registration
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Attendance records data for JavaScript
const attendanceRecords = <?php echo json_encode($attendance_records); ?>;

function toggleRegistrationType() {
    const memberRadio = document.getElementById('member_registration');
    const memberFields = document.getElementById('member_fields');
    const guestFields = document.getElementById('guest_fields');

    if (memberRadio.checked) {
        memberFields.style.display = 'block';
        guestFields.style.display = 'none';

        // Clear guest fields
        document.getElementById('guest_name').value = '';
        document.getElementById('guest_email').value = '';

        // Make member field required
        document.getElementById('member_id').required = true;
        document.getElementById('guest_name').required = false;
        document.getElementById('guest_email').required = false;
    } else {
        memberFields.style.display = 'none';
        guestFields.style.display = 'block';

        // Clear member field
        document.getElementById('member_id').value = '';

        // Make guest fields required
        document.getElementById('member_id').required = false;
        document.getElementById('guest_name').required = true;
        document.getElementById('guest_email').required = true;
    }
}

function editAttendance(attendanceId) {
    const record = attendanceRecords.find(r => r.id == attendanceId);
    if (!record) return;

    // Populate the edit form
    document.getElementById('edit_attendance_id').value = record.id;
    document.getElementById('edit_attendee_name').textContent = record.member_name || record.guest_name;
    document.getElementById('edit_attendance_status').value = record.attendance_status;
    document.getElementById('edit_notes').value = record.notes || '';

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('editAttendanceModal'));
    modal.show();
}

function deleteRegistration(attendanceId, attendeeName) {
    document.getElementById('delete_attendance_id').value = attendanceId;
    document.getElementById('delete_attendee_name').textContent = attendeeName;

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('deleteRegistrationModal'));
    modal.show();
}

// Initialize form validation
document.addEventListener('DOMContentLoaded', function() {
    // Set initial state
    toggleRegistrationType();
});
</script>

<?php include 'includes/footer.php'; ?>
