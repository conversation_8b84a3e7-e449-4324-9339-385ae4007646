<?php
// Test recurring events functionality
require_once '../config.php';
require_once '../classes/RecurringEventManager.php';

echo "<h2>Testing Recurring Events Functionality</h2>";

$recurringEventManager = new RecurringEventManager($pdo);

try {
    // Get a test event to work with
    $stmt = $pdo->query("
        SELECT id, title, event_date, is_recurring 
        FROM events 
        WHERE status = 'published' OR status = 'draft'
        ORDER BY event_date DESC 
        LIMIT 5
    ");
    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($events)) {
        echo "<p>No events found. Creating a test event...</p>";
        
        // Create a test event
        $stmt = $pdo->prepare("
            INSERT INTO events (title, description, event_date, location, created_by, status)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $testEventData = [
            'Test Recurring Event',
            'This is a test event for recurring functionality',
            date('Y-m-d H:i:s', strtotime('+1 week')), // 1 week from now
            'Test Location',
            1, // admin ID
            'published'
        ];
        
        $stmt->execute($testEventData);
        $testEventId = $pdo->lastInsertId();
        
        echo "<p>✅ Created test event with ID: $testEventId</p>";
        
        // Re-fetch events
        $stmt = $pdo->query("
            SELECT id, title, event_date, is_recurring 
            FROM events 
            ORDER BY event_date DESC 
            LIMIT 5
        ");
        $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    echo "<h3>Available Events for Testing</h3>";
    echo "<table border='1' cellpadding='10' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>ID</th><th>Title</th><th>Date</th><th>Is Recurring</th><th>Actions</th>";
    echo "</tr>";
    
    foreach ($events as $event) {
        echo "<tr>";
        echo "<td>" . $event['id'] . "</td>";
        echo "<td>" . htmlspecialchars($event['title']) . "</td>";
        echo "<td>" . date('M j, Y g:i A', strtotime($event['event_date'])) . "</td>";
        echo "<td>" . ($event['is_recurring'] ? '✅ Yes' : '❌ No') . "</td>";
        echo "<td>";
        if (!$event['is_recurring']) {
            echo "<a href='?test_create=" . $event['id'] . "' class='btn btn-sm btn-success'>Test Create Recurring</a>";
        } else {
            echo "<a href='?test_view=" . $event['id'] . "' class='btn btn-sm btn-info'>View Instances</a> ";
            echo "<a href='?test_delete=" . $event['id'] . "' class='btn btn-sm btn-danger'>Delete Series</a>";
        }
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Handle test actions
    if (isset($_GET['test_create'])) {
        $eventId = (int)$_GET['test_create'];
        echo "<h3>Testing: Create Recurring Series for Event ID $eventId</h3>";
        
        $recurrenceConfig = [
            'type' => 'weekly',
            'interval' => 1,
            'count' => 5,
            'end_date' => null
        ];
        
        $result = $recurringEventManager->createRecurringInstances($eventId, $recurrenceConfig);
        
        if ($result['success']) {
            echo "<div style='color: green; padding: 10px; border: 1px solid green; background-color: #f0fff0;'>";
            echo "<strong>✅ Success:</strong> " . htmlspecialchars($result['message']) . "<br>";
            echo "<strong>Created Instances:</strong><br>";
            foreach ($result['instances'] as $instance) {
                echo "- Instance #" . $instance['instance_number'] . " (ID: " . $instance['instance_id'] . ") on " . $instance['date'] . "<br>";
            }
            echo "</div>";
        } else {
            echo "<div style='color: red; padding: 10px; border: 1px solid red; background-color: #fff0f0;'>";
            echo "<strong>❌ Error:</strong> " . htmlspecialchars($result['message']);
            echo "</div>";
        }
    }
    
    if (isset($_GET['test_view'])) {
        $eventId = (int)$_GET['test_view'];
        echo "<h3>Testing: View Recurring Instances for Event ID $eventId</h3>";
        
        $instances = $recurringEventManager->getRecurringInstances($eventId);
        
        if (!empty($instances)) {
            echo "<table border='1' cellpadding='10' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background-color: #f0f0f0;'>";
            echo "<th>Instance #</th><th>Title</th><th>Date</th><th>Status</th><th>Exception</th>";
            echo "</tr>";
            
            foreach ($instances as $instance) {
                echo "<tr>";
                echo "<td>#" . $instance['instance_number'] . "</td>";
                echo "<td>" . htmlspecialchars($instance['title']) . "</td>";
                echo "<td>" . date('M j, Y g:i A', strtotime($instance['event_date'])) . "</td>";
                echo "<td>" . ucfirst($instance['status']) . "</td>";
                echo "<td>" . ($instance['is_exception'] ? '⚠️ Exception' : '✅ Normal') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No instances found for this event.</p>";
        }
    }
    
    if (isset($_GET['test_delete'])) {
        $eventId = (int)$_GET['test_delete'];
        echo "<h3>Testing: Delete Recurring Series for Event ID $eventId</h3>";
        
        $result = $recurringEventManager->deleteRecurringSeries($eventId, true);
        
        if ($result['success']) {
            echo "<div style='color: green; padding: 10px; border: 1px solid green; background-color: #f0fff0;'>";
            echo "<strong>✅ Success:</strong> " . htmlspecialchars($result['message']);
            echo "</div>";
        } else {
            echo "<div style='color: red; padding: 10px; border: 1px solid red; background-color: #fff0f0;'>";
            echo "<strong>❌ Error:</strong> " . htmlspecialchars($result['message']);
            echo "</div>";
        }
    }
    
    // Test recurring event detection
    echo "<h3>Testing: Recurring Event Detection</h3>";
    if (!empty($events)) {
        $testEvent = $events[0];
        $recurringInfo = $recurringEventManager->isRecurringEvent($testEvent['id']);
        
        echo "<p><strong>Event:</strong> " . htmlspecialchars($testEvent['title']) . " (ID: " . $testEvent['id'] . ")</p>";
        echo "<p><strong>Is Parent Event:</strong> " . ($recurringInfo['is_parent'] ? 'Yes' : 'No') . "</p>";
        echo "<p><strong>Is Instance:</strong> " . ($recurringInfo['is_instance'] ? 'Yes' : 'No') . "</p>";
        if ($recurringInfo['parent_id']) {
            echo "<p><strong>Parent Event ID:</strong> " . $recurringInfo['parent_id'] . "</p>";
        }
    }
    
    echo "<h3>✅ Recurring Events Testing Complete!</h3>";
    
} catch (Exception $e) {
    echo "<h3>❌ Error during testing: " . htmlspecialchars($e->getMessage()) . "</h3>";
    error_log("Recurring events test error: " . $e->getMessage());
}

echo "<hr>";
echo "<h3>Database Schema Verification</h3>";

try {
    // Check if recurring event tables exist
    $tables_to_check = ['recurring_event_instances'];
    
    echo "<h4>Required Tables:</h4>";
    foreach ($tables_to_check as $table) {
        try {
            $stmt = $pdo->query("SELECT 1 FROM $table LIMIT 1");
            echo "<p>✅ $table</p>";
        } catch (PDOException $e) {
            echo "<p>❌ $table - " . $e->getMessage() . "</p>";
        }
    }
    
    // Check if new columns exist in events table
    $stmt = $pdo->query("DESCRIBE events");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $required_columns = ['is_recurring', 'recurrence_type', 'recurrence_interval', 'parent_event_id'];
    
    echo "<h4>Required Columns in Events Table:</h4>";
    foreach ($required_columns as $col) {
        $exists = in_array($col, $columns);
        echo "<p>" . ($exists ? "✅" : "❌") . " $col</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error checking schema: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
