<?php
/**
 * Setup Session Notification Preferences
 * 
 * This script adds session-related notification preferences for all existing users
 */

require_once 'config.php';

echo "<h1>🔔 Setting Up Session Notification Preferences</h1>\n";
echo "<p>Adding session notification preferences for all users...</p>\n";

try {
    // First, ensure the notification_preferences table exists with the right structure
    echo "<h2>📋 Checking notification_preferences table...</h2>\n";
    
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS notification_preferences (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            notification_type VARCHAR(50) NOT NULL,
            email_enabled TINYINT(1) DEFAULT 1,
            web_enabled TINYINT(1) DEFAULT 1,
            sms_enabled TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_type (user_id, notification_type),
            INDEX idx_user_id (user_id),
            INDEX idx_notification_type (notification_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    
    echo "<p style='color: green;'>✅ notification_preferences table verified</p>\n";
    
    // Get all active users
    $stmt = $pdo->prepare("SELECT id FROM members WHERE email IS NOT NULL AND email != ''");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>👥 Found " . count($users) . " users to process</h2>\n";
    
    // Session notification types to add
    $sessionNotificationTypes = [
        'session_registration' => [
            'name' => 'Session Registration Confirmations',
            'description' => 'Notifications when you register for or cancel session registrations',
            'email_default' => 1,
            'web_default' => 1,
            'sms_default' => 0
        ],
        'session_reminder' => [
            'name' => 'Session Reminders',
            'description' => 'Reminders about upcoming sessions you are registered for',
            'email_default' => 1,
            'web_default' => 1,
            'sms_default' => 0
        ],
        'session_update' => [
            'name' => 'Session Updates',
            'description' => 'Notifications about changes to sessions you are registered for',
            'email_default' => 1,
            'web_default' => 1,
            'sms_default' => 0
        ],
        'new_session' => [
            'name' => 'New Session Announcements',
            'description' => 'Notifications when new sessions are available for registration',
            'email_default' => 1,
            'web_default' => 1,
            'sms_default' => 0
        ]
    ];
    
    $totalAdded = 0;
    $totalSkipped = 0;
    
    foreach ($users as $user) {
        $userId = $user['id'];
        
        foreach ($sessionNotificationTypes as $type => $config) {
            try {
                // Check if preference already exists
                $stmt = $pdo->prepare("
                    SELECT id FROM notification_preferences 
                    WHERE user_id = ? AND notification_type = ?
                ");
                $stmt->execute([$userId, $type]);
                
                if ($stmt->fetch()) {
                    $totalSkipped++;
                    continue;
                }
                
                // Add the preference
                $stmt = $pdo->prepare("
                    INSERT INTO notification_preferences 
                    (user_id, notification_type, email_enabled, web_enabled, sms_enabled)
                    VALUES (?, ?, ?, ?, ?)
                ");
                
                $stmt->execute([
                    $userId,
                    $type,
                    $config['email_default'],
                    $config['web_default'],
                    $config['sms_default']
                ]);
                
                $totalAdded++;
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Error adding {$type} preference for user {$userId}: " . $e->getMessage() . "</p>\n";
            }
        }
    }
    
    echo "<h2>📊 Summary</h2>\n";
    echo "<p>✅ Added preferences: <strong>$totalAdded</strong></p>\n";
    echo "<p>⏭️ Skipped (already exist): <strong>$totalSkipped</strong></p>\n";
    
    // Also create a notification types reference table for admin management
    echo "<h2>📚 Creating notification types reference...</h2>\n";
    
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS notification_types (
            id INT AUTO_INCREMENT PRIMARY KEY,
            type_key VARCHAR(50) NOT NULL UNIQUE,
            display_name VARCHAR(100) NOT NULL,
            description TEXT,
            category VARCHAR(50) DEFAULT 'general',
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_category (category),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    
    // Insert session notification types
    foreach ($sessionNotificationTypes as $type => $config) {
        try {
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO notification_types 
                (type_key, display_name, description, category, is_active)
                VALUES (?, ?, ?, 'session', 1)
            ");
            
            $stmt->execute([
                $type,
                $config['name'],
                $config['description']
            ]);
            
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Could not add notification type {$type}: " . $e->getMessage() . "</p>\n";
        }
    }
    
    // Also add other common notification types
    $commonTypes = [
        'announcement' => ['General Announcements', 'Important announcements from the organization'],
        'event' => ['Event Notifications', 'Notifications about events and activities'],
        'birthday' => ['Birthday Notifications', 'Birthday reminders and celebrations'],
        'donation' => ['Donation Receipts', 'Donation confirmations and receipts'],
        'system' => ['System Notifications', 'System updates and maintenance notices']
    ];
    
    foreach ($commonTypes as $type => $info) {
        try {
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO notification_types 
                (type_key, display_name, description, category, is_active)
                VALUES (?, ?, ?, 'general', 1)
            ");
            
            $stmt->execute([$type, $info[0], $info[1]]);
            
        } catch (Exception $e) {
            // Ignore errors for common types
        }
    }
    
    echo "<p style='color: green;'>✅ Notification types reference created</p>\n";
    
    // Create a view for easy preference management
    echo "<h2>🔍 Creating notification preferences view...</h2>\n";
    
    try {
        $pdo->exec("
            CREATE OR REPLACE VIEW user_notification_preferences_view AS
            SELECT 
                m.id as user_id,
                m.first_name,
                m.last_name,
                m.email,
                nt.type_key,
                nt.display_name,
                nt.description,
                nt.category,
                COALESCE(np.email_enabled, 1) as email_enabled,
                COALESCE(np.web_enabled, 1) as web_enabled,
                COALESCE(np.sms_enabled, 0) as sms_enabled,
                np.updated_at as preference_updated_at
            FROM members m
            CROSS JOIN notification_types nt
            LEFT JOIN notification_preferences np ON m.id = np.user_id AND nt.type_key = np.notification_type
            WHERE m.email IS NOT NULL AND m.email != ''
            AND nt.is_active = 1
            ORDER BY m.id, nt.category, nt.display_name
        ");
        
        echo "<p style='color: green;'>✅ Created user_notification_preferences_view for easy management</p>\n";
        
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ Could not create preferences view: " . $e->getMessage() . "</p>\n";
    }
    
    // Test the notification system
    echo "<h2>🧪 Testing session notification system...</h2>\n";
    
    // Get a test user
    $stmt = $pdo->prepare("SELECT id, first_name, last_name FROM members WHERE email IS NOT NULL LIMIT 1");
    $stmt->execute();
    $testUser = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($testUser) {
        require_once 'includes/notification_functions.php';
        
        $testResult = createNotification(
            $pdo,
            $testUser['id'],
            'Session Notification System Test',
            'This is a test notification to verify the session notification system is working properly.',
            'system',
            null,
            'system',
            '/user/notifications.php',
            'normal'
        );
        
        if ($testResult) {
            echo "<p style='color: green;'>✅ Test notification created successfully for {$testUser['first_name']} {$testUser['last_name']}</p>\n";
        } else {
            echo "<p style='color: red;'>❌ Failed to create test notification</p>\n";
        }
    }
    
    echo "<hr>\n";
    echo "<h2>🎉 Session Notification Preferences Setup Complete!</h2>\n";
    echo "<p>All users now have session notification preferences configured.</p>\n";
    echo "<p>Users can manage their preferences in their account settings.</p>\n";
    
    // Display some statistics
    $stmt = $pdo->prepare("
        SELECT 
            notification_type,
            COUNT(*) as user_count,
            SUM(email_enabled) as email_enabled_count,
            SUM(web_enabled) as web_enabled_count
        FROM notification_preferences 
        WHERE notification_type LIKE 'session%' OR notification_type = 'new_session'
        GROUP BY notification_type
        ORDER BY notification_type
    ");
    $stmt->execute();
    $stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($stats)) {
        echo "<h3>📊 Session Notification Statistics:</h3>\n";
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>\n";
        echo "<tr><th>Notification Type</th><th>Total Users</th><th>Email Enabled</th><th>Web Enabled</th></tr>\n";
        
        foreach ($stats as $stat) {
            echo "<tr>";
            echo "<td>{$stat['notification_type']}</td>";
            echo "<td>{$stat['user_count']}</td>";
            echo "<td>{$stat['email_enabled_count']}</td>";
            echo "<td>{$stat['web_enabled_count']}</td>";
            echo "</tr>\n";
        }
        
        echo "</table>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Critical Error: " . $e->getMessage() . "</p>\n";
    echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>\n";
}

?>
