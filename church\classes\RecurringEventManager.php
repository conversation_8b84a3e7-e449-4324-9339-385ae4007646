<?php

/**
 * Recurring Event Manager
 * Handles creation and management of recurring events
 */
class RecurringEventManager {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * Create recurring event instances
     * @param int $parentEventId Parent event ID
     * @param array $recurrenceConfig Recurrence configuration
     * @return array Result with success status and created instances
     */
    public function createRecurringInstances($parentEventId, $recurrenceConfig) {
        try {
            $this->pdo->beginTransaction();
            
            // Get parent event details
            $stmt = $this->pdo->prepare("SELECT * FROM events WHERE id = ?");
            $stmt->execute([$parentEventId]);
            $parentEvent = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$parentEvent) {
                throw new Exception("Parent event not found");
            }
            
            // Update parent event with recurrence settings
            $this->updateEventRecurrenceSettings($parentEventId, $recurrenceConfig);
            
            // Generate instances
            $instances = $this->generateRecurrenceInstances($parentEvent, $recurrenceConfig);
            $createdInstances = [];
            
            foreach ($instances as $index => $instanceDate) {
                $instanceId = $this->createEventInstance($parentEvent, $instanceDate, $index + 1);
                if ($instanceId) {
                    $createdInstances[] = [
                        'instance_id' => $instanceId,
                        'date' => $instanceDate,
                        'instance_number' => $index + 1
                    ];
                    
                    // Record in recurring_event_instances table
                    $this->recordRecurringInstance($parentEventId, $instanceId, $instanceDate, $index + 1);
                }
            }
            
            $this->pdo->commit();
            
            return [
                'success' => true,
                'message' => 'Created ' . count($createdInstances) . ' recurring event instances',
                'instances' => $createdInstances
            ];
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log("Error creating recurring instances: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Update event recurrence settings
     */
    private function updateEventRecurrenceSettings($eventId, $config) {
        $stmt = $this->pdo->prepare("
            UPDATE events 
            SET is_recurring = 1,
                recurrence_type = ?,
                recurrence_interval = ?,
                recurrence_end_date = ?,
                recurrence_count = ?,
                recurrence_data = ?
            WHERE id = ?
        ");
        
        $stmt->execute([
            $config['type'],
            $config['interval'] ?? 1,
            $config['end_date'] ?? null,
            $config['count'] ?? null,
            json_encode($config),
            $eventId
        ]);
    }
    
    /**
     * Generate recurrence instance dates
     */
    private function generateRecurrenceInstances($parentEvent, $config) {
        $instances = [];
        $startDate = new DateTime($parentEvent['event_date']);
        $currentDate = clone $startDate;
        
        $maxInstances = $config['count'] ?? 52; // Default max 52 instances
        $endDate = $config['end_date'] ? new DateTime($config['end_date']) : null;
        $interval = $config['interval'] ?? 1;
        
        for ($i = 0; $i < $maxInstances; $i++) {
            // Calculate next occurrence
            switch ($config['type']) {
                case 'daily':
                    $currentDate->add(new DateInterval('P' . $interval . 'D'));
                    break;
                case 'weekly':
                    $currentDate->add(new DateInterval('P' . ($interval * 7) . 'D'));
                    break;
                case 'monthly':
                    $currentDate->add(new DateInterval('P' . $interval . 'M'));
                    break;
                case 'yearly':
                    $currentDate->add(new DateInterval('P' . $interval . 'Y'));
                    break;
                default:
                    throw new Exception("Invalid recurrence type: " . $config['type']);
            }
            
            // Check if we've exceeded the end date
            if ($endDate && $currentDate > $endDate) {
                break;
            }
            
            // Don't create instances too far in the future (2 years max)
            $maxFutureDate = new DateTime('+2 years');
            if ($currentDate > $maxFutureDate) {
                break;
            }
            
            $instances[] = $currentDate->format('Y-m-d H:i:s');
        }
        
        return $instances;
    }
    
    /**
     * Create an event instance
     */
    private function createEventInstance($parentEvent, $instanceDate, $instanceNumber) {
        // Prepare instance data
        $instanceData = $parentEvent;
        unset($instanceData['id']); // Remove ID so new one is generated
        
        $instanceData['event_date'] = $instanceDate;
        $instanceData['parent_event_id'] = $parentEvent['id'];
        $instanceData['title'] = $parentEvent['title'] . ' #' . $instanceNumber;
        $instanceData['is_recurring'] = 0; // Instances are not recurring themselves
        $instanceData['recurrence_type'] = 'none';
        
        // Insert instance
        $columns = array_keys($instanceData);
        $placeholders = ':' . implode(', :', $columns);
        
        $sql = "INSERT INTO events (" . implode(', ', $columns) . ") VALUES (" . $placeholders . ")";
        $stmt = $this->pdo->prepare($sql);
        
        foreach ($instanceData as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }
        
        $stmt->execute();
        return $this->pdo->lastInsertId();
    }
    
    /**
     * Record recurring instance relationship
     */
    private function recordRecurringInstance($parentEventId, $instanceEventId, $instanceDate, $instanceNumber) {
        $stmt = $this->pdo->prepare("
            INSERT INTO recurring_event_instances 
            (parent_event_id, instance_event_id, instance_date, instance_number)
            VALUES (?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $parentEventId,
            $instanceEventId,
            date('Y-m-d', strtotime($instanceDate)),
            $instanceNumber
        ]);
    }
    
    /**
     * Get recurring event instances
     */
    public function getRecurringInstances($parentEventId) {
        $stmt = $this->pdo->prepare("
            SELECT rei.*, e.title, e.event_date, e.status
            FROM recurring_event_instances rei
            JOIN events e ON rei.instance_event_id = e.id
            WHERE rei.parent_event_id = ?
            ORDER BY rei.instance_date ASC
        ");
        
        $stmt->execute([$parentEventId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Delete recurring event series
     */
    public function deleteRecurringSeries($parentEventId, $deleteInstances = true) {
        try {
            $this->pdo->beginTransaction();
            
            if ($deleteInstances) {
                // Get all instance IDs
                $stmt = $this->pdo->prepare("
                    SELECT instance_event_id 
                    FROM recurring_event_instances 
                    WHERE parent_event_id = ?
                ");
                $stmt->execute([$parentEventId]);
                $instanceIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                // Delete instance events
                if (!empty($instanceIds)) {
                    $placeholders = str_repeat('?,', count($instanceIds) - 1) . '?';
                    $stmt = $this->pdo->prepare("DELETE FROM events WHERE id IN ($placeholders)");
                    $stmt->execute($instanceIds);
                }
            }
            
            // Delete recurring instance records
            $stmt = $this->pdo->prepare("DELETE FROM recurring_event_instances WHERE parent_event_id = ?");
            $stmt->execute([$parentEventId]);
            
            // Update parent event to remove recurrence
            $stmt = $this->pdo->prepare("
                UPDATE events 
                SET is_recurring = 0, recurrence_type = 'none', 
                    recurrence_data = NULL, parent_event_id = NULL
                WHERE id = ?
            ");
            $stmt->execute([$parentEventId]);
            
            $this->pdo->commit();
            
            return [
                'success' => true,
                'message' => 'Recurring series deleted successfully'
            ];
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log("Error deleting recurring series: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Update single instance (create exception)
     */
    public function updateInstance($instanceId, $changes, $reason = '') {
        try {
            $this->pdo->beginTransaction();
            
            // Update the instance event
            $setClauses = [];
            $params = [];
            
            foreach ($changes as $field => $value) {
                $setClauses[] = "$field = ?";
                $params[] = $value;
            }
            
            $params[] = $instanceId;
            
            $sql = "UPDATE events SET " . implode(', ', $setClauses) . " WHERE id = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            // Mark as exception in recurring_event_instances
            $stmt = $this->pdo->prepare("
                UPDATE recurring_event_instances 
                SET is_exception = 1, exception_reason = ?
                WHERE instance_event_id = ?
            ");
            $stmt->execute([$reason, $instanceId]);
            
            $this->pdo->commit();
            
            return [
                'success' => true,
                'message' => 'Instance updated successfully'
            ];
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log("Error updating instance: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Check if event is part of recurring series
     */
    public function isRecurringEvent($eventId) {
        $stmt = $this->pdo->prepare("
            SELECT is_recurring, parent_event_id 
            FROM events 
            WHERE id = ?
        ");
        $stmt->execute([$eventId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return [
            'is_parent' => $result && $result['is_recurring'],
            'is_instance' => $result && $result['parent_event_id'],
            'parent_id' => $result['parent_event_id'] ?? null
        ];
    }
}
