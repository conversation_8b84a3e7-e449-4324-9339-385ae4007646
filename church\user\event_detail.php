<?php
/**
 * Event Detail Page
 *
 * Displays detailed information about a specific event
 */

// Include configuration first (which sets up session configuration)
require_once '../config.php';

// Start session after configuration is loaded
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include classes
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];

// Add formatFileSize function
if (!function_exists('formatFileSize')) {
    function formatFileSize($bytes) {
        if ($bytes == 0) return '0 Bytes';

        $k = 1024;
        $sizes = ['Bytes', 'KB', 'MB', 'GB'];
        $i = floor(log($bytes) / log($k));

        return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
    }
}

// Add function to fix file paths
if (!function_exists('fixFilePath')) {
    function fixFilePath($filePath) {
        // If path already starts with uploads/, it's correct
        if (strpos($filePath, 'uploads/') === 0) {
            return $filePath;
        }

        // If it's a full path, extract just the filename and reconstruct
        $filename = basename($filePath);

        // Check if it's an event file
        if (strpos($filename, 'event_') === 0) {
            return 'uploads/events/' . $filename;
        }

        // Default to uploads directory
        return 'uploads/' . $filename;
    }
}

// Get user data from members table
$stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
$stmt->execute([$userId]);
$userData = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$userData) {
    session_destroy();
    header("Location: login.php");
    exit();
}

// Get site settings for branding
$sitename = get_organization_name() . ' - Event Details';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$siteNameResult = $stmt->fetch();
if ($siteNameResult) {
    $sitename = $siteNameResult['setting_value'] . ' - Event Details';
}

// Get event ID from URL
$eventId = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
if (!$eventId) {
    header('Location: events.php');
    exit();
}

try {
    // Get event details
    try {
        $stmt = $pdo->prepare("
            SELECT e.*,
                   (
                       (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = e.id AND er.status = 'attending') +
                       (SELECT COUNT(*) FROM event_rsvps_guests erg WHERE erg.event_id = e.id AND erg.status = 'attending')
                   ) as attending_count,
                   (
                       (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = e.id AND er.status = 'maybe') +
                       (SELECT COUNT(*) FROM event_rsvps_guests erg WHERE erg.event_id = e.id AND erg.status = 'maybe')
                   ) as maybe_count,
                   (
                       (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = e.id AND er.status = 'not_attending') +
                       (SELECT COUNT(*) FROM event_rsvps_guests erg WHERE erg.event_id = e.id AND erg.status = 'not_attending')
                   ) as not_attending_count
            FROM events e
            WHERE e.id = ? AND e.is_active = 1
        ");
        $stmt->execute([$eventId]);
        $event = $stmt->fetch(PDO::FETCH_ASSOC);

        // Get banner information separately to avoid GROUP BY issues
        if ($event) {
            $bannerStmt = $pdo->prepare("
                SELECT file_path, file_type, file_name, alt_text
                FROM event_files
                WHERE event_id = ? AND is_header_banner = 1
                LIMIT 1
            ");
            $bannerStmt->execute([$eventId]);
            $banner = $bannerStmt->fetch(PDO::FETCH_ASSOC);

            if ($banner) {
                $event['header_banner_path'] = $banner['file_path'];
                $event['header_banner_type'] = $banner['file_type'];
                $event['header_banner_name'] = $banner['file_name'];
                $event['header_banner_alt'] = $banner['alt_text'];
            } else {
                $event['header_banner_path'] = null;
                $event['header_banner_type'] = null;
                $event['header_banner_name'] = null;
                $event['header_banner_alt'] = null;
            }
        }
    } catch (PDOException $e) {
        // Fallback query without header banner fields
        $stmt = $pdo->prepare("
            SELECT e.*,
                   COUNT(CASE WHEN er.status = 'attending' THEN 1 END) as attending_count,
                   COUNT(CASE WHEN er.status = 'maybe' THEN 1 END) as maybe_count,
                   COUNT(CASE WHEN er.status = 'not_attending' THEN 1 END) as not_attending_count
            FROM events e
            LEFT JOIN event_rsvps er ON e.id = er.event_id
            WHERE e.id = ? AND e.is_active = 1
            GROUP BY e.id
        ");
        $stmt->execute([$eventId]);
        $event = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($event) {
            $event['header_banner_path'] = null;
            $event['header_banner_type'] = null;
            $event['header_banner_name'] = null;
            $event['header_banner_alt'] = null;
        }
    }
    
    if (!$event) {
        header('Location: events.php');
        exit();
    }
    
    // Get user's RSVP status
    $stmt = $pdo->prepare("SELECT status, notes FROM event_rsvps WHERE event_id = ? AND user_id = ?");
    $stmt->execute([$eventId, $userId]);
    $userRsvp = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get attendee list (only for attending users)
    $stmt = $pdo->prepare("
        SELECT m.first_name, m.last_name, er.status, er.created_at
        FROM event_rsvps er
        JOIN members m ON er.user_id = m.id
        WHERE er.event_id = ? AND er.status = 'attending'
        ORDER BY er.created_at ASC
    ");
    $stmt->execute([$eventId]);
    $attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get event promotional materials and documents
    $eventMaterials = [];
    try {
        $stmt = $pdo->prepare("
            SELECT id, file_name, file_path, file_type, file_size, file_category,
                   is_header_banner, alt_text, display_order, upload_date
            FROM event_files
            WHERE event_id = ?
            ORDER BY is_header_banner DESC, display_order ASC, upload_date DESC
        ");
        $stmt->execute([$eventId]);
        $eventMaterials = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error loading event materials: " . $e->getMessage());
        $eventMaterials = [];
    }

    // Check if event is in the future (event_date is already datetime)
    $isFutureEvent = strtotime($event['event_date']) > time();
    
} catch (PDOException $e) {
    error_log("Event detail error: " . $e->getMessage());
    header('Location: events.php');
    exit();
}

// Get site settings for branding
$sitename = 'Church Management System';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$result = $stmt->fetch();
if ($result) {
    $sitename = $result['setting_value'];
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Details - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="../css/promotional-materials.css">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'includes/theme_css.php'; ?>

    <style>
        body {
            background-color: var(--bs-body-bg, #f8f9fa);
            color: var(--bs-body-color, #212529);
            font-family: var(--bs-font-sans-serif, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }

        .navbar {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            color: white !important;
            display: flex;
            align-items: center;
        }

        .navbar-logo {
            max-height: 40px;
            max-width: 150px;
            height: auto;
            width: auto;
            object-fit: contain;
        }

        /* Responsive logo adjustments */
        @media (max-width: 768px) {
            .navbar-logo {
                max-height: 32px;
                max-width: 120px;
            }

            .navbar-brand {
                font-size: 1rem;
            }
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
        }

        .navbar-nav .nav-link.active {
            color: white !important;
            font-weight: 600;
        }

        .dropdown-menu {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
            transition: background-color 0.15s ease-in-out;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .dropdown-item i {
            width: 16px;
            margin-right: 8px;
        }



        .event-container {
            margin-top: 2rem;
        }

        .content-header {
            margin-bottom: 2rem;
        }

        .card {
            border: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border-radius: 15px;
        }

        .event-card {
            background: white;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .event-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #6c757d;
        }

        .attendee-item {
            padding: 0.5rem 0;
            border-bottom: 1px solid #f8f9fa;
        }

        .attendee-item:last-child {
            border-bottom: none;
        }

        .attendee-avatar i {
            font-size: 1.5rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container event-container">
            <div class="content-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1><i class="bi bi-calendar-event"></i> Event Details</h1>
                        <p class="text-muted">View event information and manage your RSVP</p>
                    </div>
                    <a href="events.php" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-left"></i> Back to Events
                    </a>
                </div>
            </div>

            <!-- Header Banner Section -->
            <?php if (!empty($event['header_banner_path'])): ?>
                <?php $bannerPath = fixFilePath($event['header_banner_path']); ?>
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card border-0 shadow-sm">
                            <?php if (!empty($event['header_banner_type']) && strpos($event['header_banner_type'], 'image/') === 0): ?>
                                <img src="../<?= htmlspecialchars($bannerPath) ?>"
                                     alt="<?= htmlspecialchars(($event['header_banner_alt'] ?? '') ?: $event['title']) ?>"
                                     class="card-img-top"
                                     style="height: 300px; object-fit: cover;">
                            <?php else: ?>
                                <div class="card-body text-center py-4">
                                    <i class="bi bi-file-pdf fs-1 text-danger mb-3"></i>
                                    <h6 class="card-title">Event Promotional Material</h6>
                                    <p class="text-muted mb-3"><?= htmlspecialchars($event['header_banner_name'] ?? 'Promotional Material') ?></p>
                                    <a href="../<?= htmlspecialchars($bannerPath) ?>"
                                       target="_blank"
                                       class="btn btn-outline-primary">
                                        <i class="bi bi-download"></i> View/Download PDF
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- Event Information -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><?php echo htmlspecialchars($event['title']); ?></h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <h6><i class="bi bi-calendar3"></i> Date & Time</h6>
                                    <p class="mb-0">
                                        <?php echo date('l, F j, Y', strtotime($event['event_date'])); ?><br>
                                        <small class="text-muted"><?php echo date('g:i A', strtotime($event['event_date'])); ?></small>
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="bi bi-geo-alt"></i> Location</h6>
                                    <p class="mb-0"><?php echo htmlspecialchars($event['location']); ?></p>
                                </div>
                            </div>
                            
                            <?php if ($event['max_attendees']): ?>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <h6><i class="bi bi-people"></i> Capacity</h6>
                                    <p class="mb-0">
                                        <?php echo $event['attending_count']; ?> / <?php echo $event['max_attendees']; ?> attendees
                                        <div class="progress mt-1" style="height: 6px;">
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: <?php echo ($event['attending_count'] / $event['max_attendees']) * 100; ?>%"></div>
                                        </div>
                                    </p>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <div class="mb-3">
                                <h6><i class="bi bi-info-circle"></i> Description</h6>
                                <p><?php echo nl2br(htmlspecialchars($event['description'])); ?></p>
                            </div>

                            <!-- Requirements & Notes Section -->
                            <?php if (!empty($event['requirements']) || !empty($event['notes']) || !empty($event['special_instructions'])): ?>
                            <div class="mb-4">
                                <h6><i class="bi bi-exclamation-triangle"></i> Requirements & Notes</h6>
                                <div class="alert alert-info">
                                    <?php if (!empty($event['requirements'])): ?>
                                        <div class="mb-2">
                                            <strong>Requirements:</strong><br>
                                            <?php echo nl2br(htmlspecialchars($event['requirements'])); ?>
                                        </div>
                                    <?php endif; ?>

                                    <?php if (!empty($event['notes'])): ?>
                                        <div class="mb-2">
                                            <strong>Notes:</strong><br>
                                            <?php echo nl2br(htmlspecialchars($event['notes'])); ?>
                                        </div>
                                    <?php endif; ?>

                                    <?php if (!empty($event['special_instructions'])): ?>
                                        <div class="mb-2">
                                            <strong>Special Instructions:</strong><br>
                                            <?php echo nl2br(htmlspecialchars($event['special_instructions'])); ?>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Default requirements if no specific field exists -->
                                    <?php if (empty($event['requirements']) && empty($event['notes']) && empty($event['special_instructions'])): ?>
                                        <div class="mb-2">
                                            <strong>Please come with shoes</strong><br>
                                            This is a requirement for all attendees.
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php else: ?>
                            <!-- Default Requirements & Notes Section -->
                            <div class="mb-4">
                                <h6><i class="bi bi-exclamation-triangle"></i> Requirements & Notes</h6>
                                <div class="alert alert-info">
                                    <div class="mb-2">
                                        <strong>Please come with shoes</strong><br>
                                        This is a requirement for all attendees.
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <?php if (!empty($event['image_path'])): ?>
                            <div class="mb-3">
                                <h6><i class="bi bi-image"></i> Event Image</h6>
                                <img src="../<?php echo htmlspecialchars($event['image_path']); ?>"
                                     alt="Event Image" class="img-fluid rounded" style="max-height: 300px;">
                            </div>
                            <?php endif; ?>

                            <!-- Event Materials Section -->
                            <?php if (!empty($eventMaterials)): ?>
                            <div class="mb-4">
                                <h6><i class="bi bi-folder2-open"></i> Event Materials</h6>

                                <?php
                                // Separate materials by category
                                $promotionalMaterials = [];
                                $documents = [];
                                $headerBanner = null;

                                foreach ($eventMaterials as $material) {
                                    if ($material['is_header_banner']) {
                                        $headerBanner = $material;
                                    } elseif ($material['file_category'] === 'promotional') {
                                        $promotionalMaterials[] = $material;
                                    } else {
                                        $documents[] = $material;
                                    }
                                }
                                ?>

                                <!-- Promotional Materials -->
                                <?php if (!empty($promotionalMaterials)): ?>
                                <div class="mb-3">
                                    <h6 class="text-muted mb-2"><i class="bi bi-images"></i> Promotional Materials</h6>
                                    <div class="row">
                                        <?php foreach ($promotionalMaterials as $material): ?>
                                        <div class="col-md-6 col-lg-4 mb-3">
                                            <div class="card border-0 shadow-sm">
                                                <?php if (strpos($material['file_type'], 'image/') === 0): ?>
                                                    <?php $filePath = fixFilePath($material['file_path']); ?>
                                                    <img src="../<?php echo htmlspecialchars($filePath); ?>"
                                                         class="card-img-top"
                                                         style="height: 200px; object-fit: cover;"
                                                         alt="<?php echo htmlspecialchars($material['alt_text'] ?: $material['file_name']); ?>"
                                                         data-bs-toggle="modal"
                                                         data-bs-target="#imageModal"
                                                         data-image-src="../<?php echo htmlspecialchars($material['file_path']); ?>"
                                                         data-image-title="<?php echo htmlspecialchars($material['file_name']); ?>"
                                                         style="cursor: pointer;">
                                                <?php else: ?>
                                                    <div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 200px;">
                                                        <i class="bi bi-file-earmark-pdf display-1 text-primary"></i>
                                                    </div>
                                                <?php endif; ?>
                                                <div class="card-body p-2">
                                                    <h6 class="card-title mb-1 small"><?php echo htmlspecialchars($material['file_name']); ?></h6>
                                                    <p class="card-text small text-muted mb-2">
                                                        Size: <?php echo formatFileSize($material['file_size']); ?>
                                                    </p>
                                                    <a href="../<?php echo htmlspecialchars($filePath); ?>"
                                                       class="btn btn-sm btn-outline-primary"
                                                       target="_blank"
                                                       download="<?php echo htmlspecialchars($material['file_name']); ?>">
                                                        <i class="bi bi-download"></i> Download
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <!-- Documents -->
                                <?php if (!empty($documents)): ?>
                                <div class="mb-3">
                                    <h6 class="text-muted mb-2"><i class="bi bi-file-earmark-text"></i> Documents</h6>
                                    <div class="list-group">
                                        <?php foreach ($documents as $document): ?>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-file-earmark-pdf text-danger me-3"></i>
                                                <div>
                                                    <h6 class="mb-0"><?php echo htmlspecialchars($document['file_name']); ?></h6>
                                                    <small class="text-muted">
                                                        Size: <?php echo formatFileSize($document['file_size']); ?> |
                                                        Uploaded: <?php echo date('M j, Y', strtotime($document['upload_date'])); ?>
                                                    </small>
                                                </div>
                                            </div>
                                            <div>
                                                <?php $docFilePath = fixFilePath($document['file_path']); ?>
                                                <a href="../<?php echo htmlspecialchars($docFilePath); ?>"
                                                   class="btn btn-sm btn-outline-primary me-2"
                                                   target="_blank">
                                                    <i class="bi bi-eye"></i> View
                                                </a>
                                                <a href="../<?php echo htmlspecialchars($docFilePath); ?>"
                                                   class="btn btn-sm btn-primary"
                                                   download="<?php echo htmlspecialchars($document['file_name']); ?>">
                                                    <i class="bi bi-download"></i> Download
                                                </a>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- RSVP and Attendees -->
                <div class="col-lg-4">
                    <!-- RSVP Section -->
                    <?php if ($isFutureEvent): ?>
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0"><i class="bi bi-check-circle"></i> RSVP Status</h6>
                        </div>
                        <div class="card-body">
                            <?php if ($userRsvp): ?>
                                <div class="alert alert-info">
                                    <strong>Your RSVP:</strong> 
                                    <?php 
                                    $statusLabels = [
                                        'attending' => 'Attending',
                                        'maybe' => 'Maybe',
                                        'not_attending' => 'Not Attending'
                                    ];
                                    echo $statusLabels[$userRsvp['status']];
                                    ?>
                                    <?php if ($userRsvp['notes']): ?>
                                        <br><small><?php echo htmlspecialchars($userRsvp['notes']); ?></small>
                                    <?php endif; ?>
                                </div>
                                <button type="button" class="btn btn-primary btn-sm" onclick="updateRsvp(<?php echo $eventId; ?>)">
                                    Update RSVP
                                </button>
                            <?php else: ?>
                                <p class="text-muted">You haven't RSVP'd to this event yet.</p>
                                <button type="button" class="btn btn-success" onclick="rsvpEvent(<?php echo $eventId; ?>)">
                                    RSVP Now
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php else: ?>
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="alert alert-secondary">
                                <i class="bi bi-clock-history"></i> This event has already occurred.
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Event Statistics -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0"><i class="bi bi-bar-chart"></i> RSVP Summary</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="stat-number text-success"><?php echo $event['attending_count']; ?></div>
                                        <div class="stat-label">Attending</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="stat-number text-warning"><?php echo $event['maybe_count']; ?></div>
                                        <div class="stat-label">Maybe</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="stat-number text-danger"><?php echo $event['not_attending_count']; ?></div>
                                        <div class="stat-label">Not Attending</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Attendees List -->
                    <?php if (!empty($attendees)): ?>
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0"><i class="bi bi-people"></i> Attendees (<?php echo count($attendees); ?>)</h6>
                        </div>
                        <div class="card-body">
                            <div class="attendees-list">
                                <?php foreach ($attendees as $attendee): ?>
                                <div class="attendee-item d-flex align-items-center mb-2">
                                    <div class="attendee-avatar me-2">
                                        <i class="bi bi-person-circle text-muted"></i>
                                    </div>
                                    <div class="attendee-info">
                                        <div class="attendee-name">
                                            <?php echo htmlspecialchars($attendee['first_name'] . ' ' . $attendee['last_name']); ?>
                                        </div>
                                        <small class="text-muted">
                                            RSVP'd <?php echo date('M j', strtotime($attendee['created_at'])); ?>
                                        </small>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
    </div>

<!-- RSVP Modal -->
<div class="modal fade" id="rsvpModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">RSVP for Event</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="rsvpForm">
                    <input type="hidden" id="rsvp_event_id" name="event_id" value="<?php echo $eventId; ?>">

                    <div class="mb-3">
                        <label class="form-label">RSVP Status</label>
                        <div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="rsvp_response" id="attending" value="attending" 
                                       <?php echo ($userRsvp && $userRsvp['status'] === 'attending') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="attending">
                                    <i class="bi bi-check-circle text-success"></i> I will attend
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="rsvp_response" id="maybe" value="maybe"
                                       <?php echo ($userRsvp && $userRsvp['status'] === 'maybe') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="maybe">
                                    <i class="bi bi-question-circle text-warning"></i> Maybe
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="rsvp_response" id="not_attending" value="not_attending"
                                       <?php echo ($userRsvp && $userRsvp['status'] === 'not_attending') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="not_attending">
                                    <i class="bi bi-x-circle text-danger"></i> Cannot attend
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="rsvp_notes" class="form-label">Notes (Optional)</label>
                        <textarea class="form-control" id="rsvp_notes" name="notes" rows="3" 
                                  placeholder="Any special requirements or comments..."><?php echo $userRsvp ? htmlspecialchars($userRsvp['notes']) : ''; ?></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitRSVP()">Submit RSVP</button>
            </div>
        </div>
    </div>
</div>

<script>
    function rsvpEvent(eventId, defaultStatus = 'attending') {
        document.getElementById('rsvp_event_id').value = eventId;
        
        // Set default radio button if no current RSVP
        <?php if (!$userRsvp): ?>
        document.querySelector(`input[name="rsvp_response"][value="${defaultStatus}"]`).checked = true;
        <?php endif; ?>

        new bootstrap.Modal(document.getElementById('rsvpModal')).show();
    }

    function updateRsvp(eventId) {
        rsvpEvent(eventId);
    }

    function submitRSVP() {
        const form = document.getElementById('rsvpForm');
        const formData = new FormData(form);

        // Get selected response
        const selectedResponse = document.querySelector('input[name="rsvp_response"]:checked');
        if (!selectedResponse) {
            alert('Please select an RSVP status.');
            return;
        }
        
        formData.set('status', selectedResponse.value);
        formData.append('action', 'rsvp');

        fetch('rsvp_handler.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('RSVP updated successfully!');
                bootstrap.Modal.getInstance(document.getElementById('rsvpModal')).hide();
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred. Please try again.');
        });
    }

    // Image modal functionality
    document.addEventListener('DOMContentLoaded', function() {
        const imageModal = document.getElementById('imageModal');
        if (imageModal) {
            imageModal.addEventListener('show.bs.modal', function(event) {
                const trigger = event.relatedTarget;
                const imageSrc = trigger.getAttribute('data-image-src');
                const imageTitle = trigger.getAttribute('data-image-title');

                const modalImage = imageModal.querySelector('#modalImage');
                const modalTitle = imageModal.querySelector('#modalImageTitle');

                modalImage.src = imageSrc;
                modalTitle.textContent = imageTitle;
            });
        }
    });
</script>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">
                    <i class="bi bi-image"></i> <span id="modalImageTitle">Image</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" class="img-fluid" alt="Event Material">
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
<?php include 'includes/footer.php'; ?>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
