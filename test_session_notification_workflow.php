<?php
/**
 * Test Session Notification Workflow
 * 
 * This script tests the complete session notification system end-to-end
 */

require_once 'config.php';
require_once 'classes/SessionNotificationManager.php';
require_once 'classes/SessionNotificationLogger.php';

echo "<h1>🧪 Session Notification Workflow Test</h1>\n";
echo "<p>Testing the complete session notification system...</p>\n";

$testResults = [];
$sessionNotificationManager = new SessionNotificationManager($pdo);
$logger = new SessionNotificationLogger($pdo);

function testResult($testName, $success, $message = '') {
    global $testResults;
    $testResults[] = [
        'name' => $testName,
        'success' => $success,
        'message' => $message
    ];
    
    $icon = $success ? '✅' : '❌';
    $color = $success ? 'green' : 'red';
    echo "<p style='color: $color;'>$icon $testName" . ($message ? ": $message" : "") . "</p>\n";
}

try {
    echo "<h2>📋 Step 1: Database Setup Verification</h2>\n";
    
    // Test 1: Check required tables exist
    $requiredTables = [
        'event_sessions',
        'session_attendance', 
        'email_templates',
        'email_queue',
        'notifications',
        'notification_preferences',
        'session_notification_logs'
    ];
    
    foreach ($requiredTables as $table) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        $exists = $stmt->fetch() !== false;
        testResult("Table '$table' exists", $exists);
    }
    
    // Test 2: Check session email templates exist
    $requiredTemplates = [
        'Session Registration Confirmation',
        'Session Reminder - 24 Hours',
        'New Session Available',
        'Session Update Notification',
        'Session Cancellation Notice',
        'Admin: New Session Registration'
    ];
    
    foreach ($requiredTemplates as $templateName) {
        $stmt = $pdo->prepare("SELECT id FROM email_templates WHERE template_name = ?");
        $stmt->execute([$templateName]);
        $exists = $stmt->fetch() !== false;
        testResult("Email template '$templateName' exists", $exists);
    }
    
    echo "<h2>👤 Step 2: Test Data Setup</h2>\n";
    
    // Create test event if it doesn't exist
    $stmt = $pdo->prepare("SELECT id FROM events WHERE title = 'Test Session Notification Event' LIMIT 1");
    $stmt->execute();
    $testEvent = $stmt->fetch();
    
    if (!$testEvent) {
        $stmt = $pdo->prepare("
            INSERT INTO events (title, description, event_date, location, max_attendees, created_by, is_active)
            VALUES (?, ?, ?, ?, ?, 1, 1)
        ");
        $stmt->execute([
            'Test Session Notification Event',
            'This is a test event for session notification testing',
            date('Y-m-d H:i:s', strtotime('+7 days')),
            'Test Location',
            100
        ]);
        $testEventId = $pdo->lastInsertId();
        testResult("Created test event", true, "Event ID: $testEventId");
    } else {
        $testEventId = $testEvent['id'];
        testResult("Using existing test event", true, "Event ID: $testEventId");
    }
    
    // Create test session
    $stmt = $pdo->prepare("
        INSERT INTO event_sessions (
            event_id, session_title, session_description, start_datetime,
            end_datetime, max_attendees, location, instructor_name, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
    ");
    
    $sessionStartTime = date('Y-m-d H:i:s', strtotime('+2 days 10:00'));
    $sessionEndTime = date('Y-m-d H:i:s', strtotime('+2 days 11:00'));
    
    $stmt->execute([
        $testEventId,
        'Test Session for Notifications',
        'This is a test session to verify the notification system',
        $sessionStartTime,
        $sessionEndTime,
        20,
        'Test Room',
        'Test Instructor'
    ]);
    
    $testSessionId = $pdo->lastInsertId();
    testResult("Created test session", true, "Session ID: $testSessionId");
    
    // Get test user
    $stmt = $pdo->prepare("SELECT id, email, first_name, last_name FROM members WHERE email IS NOT NULL LIMIT 1");
    $stmt->execute();
    $testUser = $stmt->fetch();
    
    if (!$testUser) {
        testResult("Get test user", false, "No users with email found in database");
        throw new Exception("No test user available");
    }
    
    testResult("Found test user", true, "{$testUser['first_name']} {$testUser['last_name']} ({$testUser['email']})");
    
    echo "<h2>📧 Step 3: Email Template Testing</h2>\n";
    
    // Test 3: Registration confirmation
    $result = $sessionNotificationManager->sendRegistrationConfirmation($testSessionId, $testUser['id']);
    testResult("Registration confirmation email", $result['success'], $result['message']);
    
    // Test 4: Admin notification
    $result = $sessionNotificationManager->notifyAdminNewRegistration($testSessionId, $testUser['id']);
    testResult("Admin registration notification", $result['success'], 
               isset($result['sent']) ? "Sent to {$result['sent']} admins" : $result['message']);
    
    // Test 5: Session reminder (will likely fail due to timing, but tests the system)
    $result = $sessionNotificationManager->sendSessionReminder($testSessionId, $testUser['id']);
    testResult("Session reminder", $result['success'], $result['message']);
    
    // Test 6: New session announcement
    $result = $sessionNotificationManager->notifyNewSession($testSessionId, 'all');
    testResult("New session announcement", $result['success'], 
               isset($result['sent']) ? "Sent to {$result['sent']} users" : $result['message']);
    
    // Test 7: Session update
    $result = $sessionNotificationManager->sendSessionUpdate($testSessionId, "This is a test update message");
    testResult("Session update notification", $result['success'], 
               isset($result['sent']) ? "Sent to {$result['sent']} attendees" : $result['message']);
    
    echo "<h2>🔔 Step 4: In-App Notification Testing</h2>\n";
    
    // Check if in-app notifications were created
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count FROM notifications 
        WHERE recipient_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
    ");
    $stmt->execute([$testUser['id']]);
    $notificationCount = $stmt->fetch()['count'];
    
    testResult("In-app notifications created", $notificationCount > 0, "Created $notificationCount notifications");
    
    echo "<h2>📊 Step 5: Logging System Testing</h2>\n";
    
    // Check notification logs
    $stats = $logger->getSessionNotificationStats($testSessionId);
    testResult("Notification logging", !empty($stats), "Logged " . count($stats) . " notification types");
    
    // Test log cleanup
    $cleanedCount = $logger->cleanupOldLogs(1); // Clean logs older than 1 day
    testResult("Log cleanup functionality", true, "Cleaned $cleanedCount old log entries");
    
    echo "<h2>⚙️ Step 6: Notification Preferences Testing</h2>\n";
    
    // Check user notification preferences
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count FROM notification_preferences 
        WHERE user_id = ? AND notification_type LIKE 'session%'
    ");
    $stmt->execute([$testUser['id']]);
    $prefCount = $stmt->fetch()['count'];
    
    testResult("Session notification preferences", $prefCount > 0, "User has $prefCount session preferences");
    
    echo "<h2>🎯 Step 7: Email Queue Testing</h2>\n";
    
    // Check email queue
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count FROM email_queue 
        WHERE email_type LIKE 'session%' AND created_at >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
    ");
    $stmt->execute();
    $queueCount = $stmt->fetch()['count'];
    
    testResult("Emails queued for sending", $queueCount > 0, "$queueCount emails in queue");
    
    echo "<h2>🧹 Step 8: Cleanup Test Data</h2>\n";
    
    // Clean up test data
    $stmt = $pdo->prepare("DELETE FROM session_attendance WHERE session_id = ?");
    $stmt->execute([$testSessionId]);
    
    $stmt = $pdo->prepare("DELETE FROM event_sessions WHERE id = ?");
    $stmt->execute([$testSessionId]);
    
    $stmt = $pdo->prepare("DELETE FROM events WHERE id = ? AND title = 'Test Session Notification Event'");
    $stmt->execute([$testEventId]);
    
    testResult("Cleanup test data", true, "Removed test session and event");
    
    echo "<hr>\n";
    echo "<h2>📈 Test Summary</h2>\n";
    
    $totalTests = count($testResults);
    $passedTests = count(array_filter($testResults, function($test) { return $test['success']; }));
    $failedTests = $totalTests - $passedTests;
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>\n";
    echo "<h3>Results:</h3>\n";
    echo "<p><strong>Total Tests:</strong> $totalTests</p>\n";
    echo "<p style='color: green;'><strong>Passed:</strong> $passedTests</p>\n";
    echo "<p style='color: red;'><strong>Failed:</strong> $failedTests</p>\n";
    
    $successRate = round(($passedTests / $totalTests) * 100, 1);
    echo "<p><strong>Success Rate:</strong> $successRate%</p>\n";
    echo "</div>\n";
    
    if ($failedTests > 0) {
        echo "<h3>❌ Failed Tests:</h3>\n";
        foreach ($testResults as $test) {
            if (!$test['success']) {
                echo "<p style='color: red;'>• {$test['name']}: {$test['message']}</p>\n";
            }
        }
    }
    
    if ($successRate >= 80) {
        echo "<h2 style='color: green;'>🎉 Session Notification System Test PASSED!</h2>\n";
        echo "<p>The session notification system is working correctly.</p>\n";
    } else {
        echo "<h2 style='color: red;'>⚠️ Session Notification System Test FAILED!</h2>\n";
        echo "<p>There are issues that need to be addressed before the system is ready for production.</p>\n";
    }
    
    echo "<h3>📋 Next Steps:</h3>\n";
    echo "<ul>\n";
    echo "<li>✅ Run the session reminders cron job: <a href='cron/session_reminders.php?cron_key=fac_2024_secure_cron_8x9q2p5m' target='_blank'>Run Now</a></li>\n";
    echo "<li>✅ Check email queue processing: <a href='cron/process_email_queue.php?cron_key=fac_2024_secure_cron_8x9q2p5m' target='_blank'>Process Queue</a></li>\n";
    echo "<li>✅ Review notification logs: <a href='admin/session_notifications.php'>Admin Panel</a></li>\n";
    echo "<li>✅ Test user notification preferences: <a href='user/settings.php'>User Settings</a></li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>💥 Critical Test Error</h2>\n";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>Stack trace:</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}

?>
