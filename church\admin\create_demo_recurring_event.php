<?php
require_once '../config.php';
require_once '../classes/RecurringEventManager.php';

echo "<h1>Creating Demo Recurring Event</h1>\n";

try {
    $recurringEventManager = new RecurringEventManager($pdo);
    
    // Get a valid category ID
    $stmt = $pdo->query("SELECT id FROM event_categories LIMIT 1");
    $category = $stmt->fetch(PDO::FETCH_ASSOC);
    $categoryId = $category ? $category['id'] : null;
    
    // Create a demo recurring event
    $stmt = $pdo->prepare("
        INSERT INTO events (title, description, event_date, location, category_id, status, is_active, created_by)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $eventData = [
        'Weekly Prayer Meeting',
        'Join us every Wednesday for our weekly prayer meeting. A time of fellowship, worship, and intercession for our community and beyond.',
        '2025-07-23 19:00:00', // Next Wednesday
        'Main Sanctuary',
        $categoryId,
        'published',
        1,
        1
    ];
    
    $stmt->execute($eventData);
    $eventId = $pdo->lastInsertId();
    
    echo "<p>✓ Created demo event with ID: {$eventId}</p>\n";
    
    // Make it recurring (weekly for 12 weeks)
    $recurrenceConfig = [
        'type' => 'weekly',
        'interval' => 1,
        'count' => 12,
        'end_date' => null
    ];
    
    $result = $recurringEventManager->createRecurringInstances($eventId, $recurrenceConfig);
    
    if ($result['success']) {
        echo "<p>✓ Successfully set up as recurring event</p>\n";
        
        // Show next few occurrences
        $occurrences = $recurringEventManager->calculateRecurringDates($eventId, 5);
        
        echo "<h3>Next 5 Occurrences:</h3>\n";
        echo "<ul>\n";
        foreach ($occurrences as $occurrence) {
            $date = new DateTime($occurrence['date']);
            echo "<li>{$date->format('l, M j, Y \a\t g:i A')}</li>\n";
        }
        echo "</ul>\n";
        
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 20px 0;'>\n";
        echo "<h3>✅ Demo Event Created Successfully!</h3>\n";
        echo "<p>You can now check the admin events page to see:</p>\n";
        echo "<ul>\n";
        echo "<li>✓ Only ONE 'Weekly Prayer Meeting' entry (no duplicates)</li>\n";
        echo "<li>✓ 'Recurring' badge with repeat icon</li>\n";
        echo "<li>✓ 'Next: [date]' indicator showing next occurrence</li>\n";
        echo "<li>✓ 'Clean Display' badge indicating the new system</li>\n";
        echo "</ul>\n";
        echo "<p><strong>Visit: <a href='events.php'>Admin Events Page</a></strong></p>\n";
        echo "</div>\n";
        
    } else {
        echo "<p>✗ Failed to set up recurring event: {$result['message']}</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>\n";
}
?>
