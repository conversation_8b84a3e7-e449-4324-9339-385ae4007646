<?php
// Test script to verify event attendance search functionality
require_once '../config.php';

echo "<h2>Testing Event Attendance Search Functionality</h2>";

try {
    // Test 1: Basic events query for event attendance
    echo "<h3>Test 1: Basic events query for event attendance</h3>";
    $sql = "SELECT e.id, e.title, e.event_date, e.location, e.is_active FROM events e LIMIT 5";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "✅ Basic query successful. Found " . count($events) . " events.<br>";
    
    // Test 2: Search query for event attendance
    echo "<h3>Test 2: Search query for event attendance</h3>";
    $search_term = '%test%';
    $where_conditions = ["(e.title LIKE ? OR e.description LIKE ? OR e.location LIKE ?)"];
    $params = [$search_term, $search_term, $search_term];
    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
    
    $sql = "SELECT e.id, e.title FROM events e $where_clause LIMIT 5";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $search_results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "✅ Search query successful. Found " . count($search_results) . " matching events.<br>";
    
    // Test 3: Count query for pagination
    echo "<h3>Test 3: Count query for pagination</h3>";
    $count_sql = "SELECT COUNT(*) FROM events e $where_clause";
    $count_stmt = $pdo->prepare($count_sql);
    $count_stmt->execute($params);
    $total_events = $count_stmt->fetchColumn();
    echo "✅ Count query successful. Total matching events: " . $total_events . "<br>";
    
    // Test 4: Complex query with RSVP counts (like in event_attendance.php)
    echo "<h3>Test 4: Complex query with RSVP counts</h3>";
    $events_query = "
        SELECT
            e.id,
            e.title,
            e.event_date,
            e.location,
            e.is_active,
            (
                COALESCE(member_counts.attending_count, 0) +
                COALESCE(guest_counts.attending_count, 0)
            ) as attending_count
        FROM events e
        LEFT JOIN (
            SELECT
                event_id,
                COUNT(CASE WHEN status = 'attending' THEN 1 END) as attending_count
            FROM event_rsvps
            GROUP BY event_id
        ) member_counts ON e.id = member_counts.event_id
        LEFT JOIN (
            SELECT
                event_id,
                COUNT(CASE WHEN status = 'attending' THEN 1 END) as attending_count
            FROM event_rsvps_guests
            GROUP BY event_id
        ) guest_counts ON e.id = guest_counts.event_id
        $where_clause
        ORDER BY e.event_date DESC
        LIMIT 5
    ";
    
    $stmt = $pdo->prepare($events_query);
    $stmt->execute($params);
    $complex_results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "✅ Complex RSVP query successful. Found " . count($complex_results) . " events with RSVP data.<br>";
    
    // Test 5: Check if required tables exist
    echo "<h3>Test 5: Check required tables</h3>";
    $tables_to_check = ['events', 'event_rsvps', 'event_rsvps_guests'];
    foreach ($tables_to_check as $table) {
        try {
            $stmt = $pdo->query("SELECT 1 FROM $table LIMIT 1");
            echo "✅ Table '$table' exists and is accessible.<br>";
        } catch (PDOException $e) {
            echo "⚠️ Table '$table' issue: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<br><h3>🎉 All event attendance SQL tests passed!</h3>";
    
} catch (PDOException $e) {
    echo "<h3>❌ SQL Error: " . $e->getMessage() . "</h3>";
    echo "<p>Error Code: " . $e->getCode() . "</p>";
} catch (Exception $e) {
    echo "<h3>❌ General Error: " . $e->getMessage() . "</h3>";
}

echo "<br><hr>";
echo "<h3>Summary of Fixes Applied</h3>";
echo "<ul>";
echo "<li>✅ Fixed table alias issues in events.php search queries</li>";
echo "<li>✅ Fixed 'status' column references to use 'is_active' instead</li>";
echo "<li>✅ Added proper status filtering logic for published/draft/completed/cancelled</li>";
echo "<li>✅ Added search functionality to event_attendance.php</li>";
echo "<li>✅ Added search functionality to session_attendance.php</li>";
echo "<li>✅ Added pagination to both attendance pages</li>";
echo "<li>✅ Added admin notifications to session registration</li>";
echo "</ul>";
?>
