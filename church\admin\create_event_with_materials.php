<?php
/**
 * Create Event with Promotional Materials
 *
 * Handles event creation and promotional material uploads in a single atomic transaction
 */

// Set error reporting and handling
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors to browser
ini_set('log_errors', 1);

// Register shutdown function to catch fatal errors
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        ob_clean();
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Server error occurred: ' . $error['message'],
            'debug' => [
                'file' => $error['file'],
                'line' => $error['line'],
                'type' => $error['type']
            ]
        ]);
    }
});

session_start();

// Start output buffering to prevent any accidental output
ob_start();

// Include configuration
require_once '../config.php';

// Debug logging function
function debug_log($message) {
    error_log("[EVENT_CREATE_DEBUG] " . $message);
}

debug_log("Script started. Session data: " . print_r($_SESSION, true));
debug_log("POST data: " . print_r($_POST, true));
debug_log("FILES data: " . print_r($_FILES, true));

// Specifically debug status field
debug_log("Status field received: '" . ($_POST['status'] ?? 'NOT SET') . "'");
debug_log("Status comparison result: " . (($_POST['status'] ?? '') === 'published' ? 'TRUE' : 'FALSE'));

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    debug_log("Admin not logged in - session admin_id not set");
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized access - admin not logged in']);
    exit();
}

debug_log("Admin logged in: ID=" . $_SESSION['admin_id']);

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    debug_log("Invalid request method: " . $_SERVER['REQUEST_METHOD']);
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

try {
    debug_log("Starting event creation process");

    // Check database connection
    if (!$pdo) {
        throw new Exception("Database connection not available");
    }
    debug_log("Database connection verified");

    // Start transaction
    $pdo->beginTransaction();
    debug_log("Transaction started");

    // Validate required fields (matching the form requirements)
    $required_fields = ['title', 'start_datetime', 'end_datetime', 'location'];
    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            debug_log("Missing required field: $field");
            throw new Exception("Field '$field' is required");
        }
    }
    debug_log("Required fields validation passed");
    
    // Check if this is a recurring event
    $isRecurring = isset($_POST['is_recurring']) && $_POST['is_recurring'] === 'on';
    debug_log("Is recurring: " . ($isRecurring ? 'yes' : 'no'));
    debug_log("Status value: " . ($_POST['status'] ?? 'not set'));
    debug_log("Recurring fields: type=" . ($_POST['recurrence_type'] ?? 'not set') .
              ", interval=" . ($_POST['recurrence_interval'] ?? 'not set') .
              ", count=" . ($_POST['recurrence_count'] ?? 'not set'));

    // Create the event first
    $stmt = $pdo->prepare("
        INSERT INTO events (title, description, requirements, event_date, location,
                          max_attendees, category_id, created_by, status, is_active, is_recurring,
                          recurrence_type, recurrence_interval, recurrence_count, recurrence_end_date)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");

    $statusValue = $_POST['status'] ?? 'draft';
    $isActiveValue = ($statusValue === 'published') ? 1 : 0;

    debug_log("Setting status to: '$statusValue', is_active to: $isActiveValue");

    $executeResult = $stmt->execute([
        $_POST['title'],
        !empty($_POST['description']) ? $_POST['description'] : null,
        !empty($_POST['requirements']) ? $_POST['requirements'] : null,
        $_POST['start_datetime'],
        $_POST['location'],
        !empty($_POST['capacity']) ? (int)$_POST['capacity'] : null,
        !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null,
        $_SESSION['admin_id'],
        $statusValue,
        $isActiveValue,
        $isRecurring ? 1 : 0,
        $isRecurring ? $_POST['recurrence_type'] : null,
        $isRecurring ? (int)$_POST['recurrence_interval'] : null,
        $isRecurring ? (int)$_POST['recurrence_count'] : null,
        $isRecurring && !empty($_POST['recurrence_end_date']) ? $_POST['recurrence_end_date'] : null
    ]);

    if (!$executeResult) {
        $errorInfo = $stmt->errorInfo();
        debug_log("SQL execution failed: " . print_r($errorInfo, true));
        throw new Exception("Failed to create event: " . $errorInfo[2]);
    }
    
    $event_id = $pdo->lastInsertId();
    
    // Handle promotional material uploads if any files were provided
    $uploaded_files = [];
    $upload_errors = [];

    debug_log("FILES array: " . print_r($_FILES, true));

    // Process files from both promotional_files and event_files inputs
    $files_to_process = [];

    // Check promotional_files input
    if (!empty($_FILES['promotional_files']['name'][0])) {
        debug_log("Found promotional_files");
        for ($i = 0; $i < count($_FILES['promotional_files']['name']); $i++) {
            if ($_FILES['promotional_files']['error'][$i] === UPLOAD_ERR_OK) {
                $files_to_process[] = [
                    'name' => $_FILES['promotional_files']['name'][$i],
                    'type' => $_FILES['promotional_files']['type'][$i],
                    'tmp_name' => $_FILES['promotional_files']['tmp_name'][$i],
                    'size' => $_FILES['promotional_files']['size'][$i],
                    'error' => $_FILES['promotional_files']['error'][$i],
                    'source' => 'promotional_files'
                ];
            }
        }
    }

    // Check event_files input (Additional Documents)
    if (!empty($_FILES['event_files']['name'][0])) {
        debug_log("Found event_files");
        for ($i = 0; $i < count($_FILES['event_files']['name']); $i++) {
            if ($_FILES['event_files']['error'][$i] === UPLOAD_ERR_OK) {
                $files_to_process[] = [
                    'name' => $_FILES['event_files']['name'][$i],
                    'type' => $_FILES['event_files']['type'][$i],
                    'tmp_name' => $_FILES['event_files']['tmp_name'][$i],
                    'size' => $_FILES['event_files']['size'][$i],
                    'error' => $_FILES['event_files']['error'][$i],
                    'source' => 'event_files'
                ];
            }
        }
    }

    if (!empty($files_to_process)) {
        // Set up upload directories
        $base_dir = '../uploads/events/';
        $promotional_dir = $base_dir . 'promotional/';
        $thumbnails_dir = $base_dir . 'thumbnails/';

        // Create directories if they don't exist
        foreach ([$base_dir, $promotional_dir, $thumbnails_dir] as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }

        // Process each uploaded file
        debug_log("Processing " . count($files_to_process) . " files");

        foreach ($files_to_process as $i => $file_info) {
            try {
                $file_name = $file_info['name'];
                $file_tmp = $file_info['tmp_name'];
                $file_size = $file_info['size'];
                $file_type = $file_info['type'];
                $source = $file_info['source'];

                debug_log("Processing file: $file_name (size: $file_size, type: $file_type, source: $source)");

                // Validate file
                $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
                if (!in_array($file_type, $allowed_types)) {
                    throw new Exception("Invalid file type for $file_name. Allowed: JPEG, PNG, GIF, PDF");
                }

                // Check PHP upload limits dynamically
                $max_upload = ini_get('upload_max_filesize');
                $max_post = ini_get('post_max_size');
                $max_upload_bytes = parseSize($max_upload);
                $max_post_bytes = parseSize($max_post);
                $effective_limit = min($max_upload_bytes, $max_post_bytes);

                debug_log("File size check: $file_size bytes, PHP limit: $effective_limit bytes");

                if ($file_size > $effective_limit) {
                    throw new Exception("File $file_name is too large. Maximum allowed: " . formatBytes($effective_limit));
                }

                // Generate safe filename
                $file_extension = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
                $original_filename = pathinfo($file_name, PATHINFO_FILENAME);

                // More aggressive filename sanitization
                $safe_filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $original_filename);
                $safe_filename = preg_replace('/_+/', '_', $safe_filename); // Replace multiple underscores with single
                $safe_filename = trim($safe_filename, '_'); // Remove leading/trailing underscores

                // Ensure we have a valid filename
                if (empty($safe_filename)) {
                    $safe_filename = 'file_' . $i;
                }

                $unique_name = 'event_' . $event_id . '_' . time() . '_' . $i . '_' . $safe_filename . '.' . $file_extension;

                debug_log("Original filename: '$file_name', Safe filename: '$unique_name'");

                // Determine file category and target directory
                $file_category = in_array($file_extension, ['jpg', 'jpeg', 'png', 'gif']) ? 'promotional' : 'document';
                $target_dir = ($file_category === 'promotional') ? $promotional_dir : $base_dir;
                $file_path = $target_dir . $unique_name;

                // Move uploaded file
                debug_log("Attempting to move file from '$file_tmp' to '$file_path'");

                // Check if source file exists
                if (!file_exists($file_tmp)) {
                    throw new Exception("Temporary file not found for $file_name");
                }

                // Check if target directory is writable
                if (!is_writable($target_dir)) {
                    throw new Exception("Target directory is not writable: $target_dir");
                }

                if (!move_uploaded_file($file_tmp, $file_path)) {
                    $error = error_get_last();
                    $error_msg = $error ? $error['message'] : 'Unknown error';
                    throw new Exception("Failed to save $file_name: $error_msg");
                }

                debug_log("File successfully moved to: $file_path");

                // Generate thumbnail for images
                $thumbnail_path = null;
                if ($file_category === 'promotional') {
                    $thumbnail_path = generateThumbnail($file_path, $thumbnails_dir, $unique_name);
                }

                // Check if this should be the header banner (first image uploaded)
                $is_header_banner = ($file_category === 'promotional' && empty($uploaded_files));

                // Save to database
                $file_stmt = $pdo->prepare("
                    INSERT INTO event_files (event_id, file_name, file_path, file_type, file_size,
                                           uploaded_by, file_category, is_header_banner, display_order)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");

                $file_stmt->execute([
                    $event_id,
                    $file_name,
                    $file_path,
                    $file_type,
                    $file_size,
                    $_SESSION['admin_id'],
                    $file_category,
                    $is_header_banner ? 1 : 0,
                    $i + 1
                ]);

                $uploaded_files[] = [
                    'id' => $pdo->lastInsertId(),
                    'name' => $file_name,
                    'category' => $file_category,
                    'is_header_banner' => $is_header_banner,
                    'thumbnail' => $thumbnail_path
                ];

            } catch (Exception $e) {
                $upload_errors[] = "Error uploading $file_name: " . $e->getMessage();

                // Clean up file if it was moved
                if (isset($file_path) && file_exists($file_path)) {
                    unlink($file_path);
                }
            }
        }
    } else {
        debug_log("No files found for upload. FILES keys: " . implode(', ', array_keys($_FILES)));
        // Check if user tried to upload files but they weren't received properly
        if (!empty($_POST['has_promotional_files'])) {
            $upload_errors[] = "Files were selected but not received by server. Check file size limits.";
        }
    }

    debug_log("Upload complete. Files: " . count($uploaded_files) . ", Errors: " . count($upload_errors));

    // Commit transaction
    $pdo->commit();
    
    // Prepare response
    $response = [
        'success' => true,
        'message' => 'Event created successfully',
        'event_id' => $event_id,
        'uploaded_files' => $uploaded_files,
        'upload_errors' => $upload_errors
    ];
    
    if (!empty($upload_errors)) {
        $response['message'] .= ' with some file upload issues';
    }
    
    // Clean output buffer and send JSON response
    ob_clean();
    header('Content-Type: application/json');
    echo json_encode($response);

} catch (Exception $e) {
    debug_log("Exception caught: " . $e->getMessage());
    debug_log("Stack trace: " . $e->getTraceAsString());

    // Rollback transaction on error
    if ($pdo->inTransaction()) {
        $pdo->rollback();
        debug_log("Transaction rolled back");
    }

    // Clean up any uploaded files
    if (isset($uploaded_files)) {
        foreach ($uploaded_files as $file) {
            if (isset($file['path']) && file_exists($file['path'])) {
                unlink($file['path']);
            }
        }
        debug_log("Cleaned up uploaded files");
    }

    error_log("Event creation with materials error: " . $e->getMessage());

    // Clean output buffer and send error JSON response
    ob_clean();
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'An error occurred while creating the event: ' . $e->getMessage()]);
}

/**
 * Generate thumbnail for image files
 */
function generateThumbnail($source_path, $thumbnails_dir, $filename) {
    try {
        $thumbnail_path = $thumbnails_dir . 'thumb_' . $filename;
        
        // Get image info
        $image_info = getimagesize($source_path);
        if (!$image_info) {
            return null;
        }
        
        $width = $image_info[0];
        $height = $image_info[1];
        $type = $image_info[2];
        
        // Calculate thumbnail dimensions (max 200x200)
        $max_size = 200;
        if ($width > $height) {
            $new_width = $max_size;
            $new_height = ($height / $width) * $max_size;
        } else {
            $new_height = $max_size;
            $new_width = ($width / $height) * $max_size;
        }
        
        // Create source image
        switch ($type) {
            case IMAGETYPE_JPEG:
                $source = imagecreatefromjpeg($source_path);
                break;
            case IMAGETYPE_PNG:
                $source = imagecreatefrompng($source_path);
                break;
            case IMAGETYPE_GIF:
                $source = imagecreatefromgif($source_path);
                break;
            default:
                return null;
        }
        
        // Create thumbnail
        $thumbnail = imagecreatetruecolor($new_width, $new_height);
        
        // Preserve transparency for PNG and GIF
        if ($type == IMAGETYPE_PNG || $type == IMAGETYPE_GIF) {
            imagealphablending($thumbnail, false);
            imagesavealpha($thumbnail, true);
            $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
            imagefilledrectangle($thumbnail, 0, 0, $new_width, $new_height, $transparent);
        }
        
        imagecopyresampled($thumbnail, $source, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
        
        // Save thumbnail
        switch ($type) {
            case IMAGETYPE_JPEG:
                imagejpeg($thumbnail, $thumbnail_path, 85);
                break;
            case IMAGETYPE_PNG:
                imagepng($thumbnail, $thumbnail_path);
                break;
            case IMAGETYPE_GIF:
                imagegif($thumbnail, $thumbnail_path);
                break;
        }
        
        imagedestroy($source);
        imagedestroy($thumbnail);
        
        return $thumbnail_path;
        
    } catch (Exception $e) {
        error_log("Thumbnail generation error: " . $e->getMessage());
        return null;
    }
}

/**
 * Parse PHP size notation (like "5M", "128K") to bytes
 */
function parseSize($size) {
    $unit = preg_replace('/[^bkmgtpezy]/i', '', $size);
    $size = preg_replace('/[^0-9\.]/', '', $size);
    if ($unit) {
        return round($size * pow(1024, stripos('bkmgtpezy', $unit[0])));
    } else {
        return round($size);
    }
}

/**
 * Format bytes to human readable format
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');

    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }

    return round($bytes, $precision) . ' ' . $units[$i];
}
?>
