<?php
/**
 * Setup Session Email Templates
 * 
 * This script creates email templates for session-related notifications
 */

require_once 'config.php';

echo "<h1>🎯 Setting Up Session Email Templates</h1>\n";
echo "<p>Creating comprehensive email templates for session management...</p>\n";

// Session email templates to create
$sessionTemplates = [
    [
        'template_name' => 'Session Registration Confirmation',
        'subject' => 'Registration Confirmed: {session_title}',
        'content' => '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Session Registration Confirmation</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .session-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea; }
        .button { display: inline-block; background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✅ Registration Confirmed!</h1>
            <p>You are successfully registered for this session</p>
        </div>
        <div class="content">
            <p>Dear {member_name},</p>
            
            <p>Great news! Your registration for the following session has been confirmed:</p>
            
            <div class="session-details">
                <h3>📅 {session_title}</h3>
                <p><strong>Event:</strong> {event_title}</p>
                <p><strong>Date & Time:</strong> {session_date} at {session_time}</p>
                <p><strong>Location:</strong> {session_location}</p>
                <p><strong>Instructor:</strong> {instructor_name}</p>
                <p><strong>Duration:</strong> {session_duration}</p>
                {session_description}
            </div>
            
            <p><strong>What to expect:</strong></p>
            <ul>
                <li>Please arrive 10 minutes early for check-in</li>
                <li>Bring any materials mentioned in the session description</li>
                <li>You will receive a reminder email 24 hours before the session</li>
            </ul>
            
            <p>If you need to cancel your registration, please do so at least 24 hours in advance.</p>
            
            <a href="{my_sessions_url}" class="button">View My Sessions</a>
            
            <p>We look forward to seeing you there!</p>
            
            <p>Best regards,<br>
            {organization_name}</p>
        </div>
        <div class="footer">
            <p>This is an automated message from {organization_name}</p>
        </div>
    </div>
</body>
</html>',
        'template_category' => 'event',
        'is_birthday_template' => 0
    ],
    
    [
        'template_name' => 'Session Reminder - 24 Hours',
        'subject' => 'Reminder: {session_title} Tomorrow',
        'content' => '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Session Reminder</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .session-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f39c12; }
        .reminder-box { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .button { display: inline-block; background: #f39c12; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⏰ Session Reminder</h1>
            <p>Your session is tomorrow!</p>
        </div>
        <div class="content">
            <p>Dear {member_name},</p>
            
            <div class="reminder-box">
                <strong>🔔 Don\'t forget!</strong> You have a session scheduled for tomorrow.
            </div>
            
            <div class="session-details">
                <h3>📅 {session_title}</h3>
                <p><strong>Date & Time:</strong> {session_date} at {session_time}</p>
                <p><strong>Location:</strong> {session_location}</p>
                <p><strong>Instructor:</strong> {instructor_name}</p>
            </div>
            
            <p><strong>Preparation checklist:</strong></p>
            <ul>
                <li>✅ Mark your calendar</li>
                <li>✅ Plan to arrive 10 minutes early</li>
                <li>✅ Bring any required materials</li>
                <li>✅ Review the session description if needed</li>
            </ul>
            
            <p>If you can no longer attend, please cancel your registration as soon as possible to allow others to join.</p>
            
            <a href="{my_sessions_url}" class="button">Manage My Sessions</a>
            
            <p>See you tomorrow!</p>
            
            <p>Best regards,<br>
            {organization_name}</p>
        </div>
    </div>
</body>
</html>',
        'template_category' => 'reminder',
        'is_birthday_template' => 0
    ],
    
    [
        'template_name' => 'New Session Available',
        'subject' => 'New Session: {session_title} - Register Now!',
        'content' => '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>New Session Available</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .session-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #27ae60; }
        .new-badge { background: #e74c3c; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px; font-weight: bold; }
        .button { display: inline-block; background: #27ae60; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🆕 New Session Available!</h1>
            <span class="new-badge">JUST ADDED</span>
        </div>
        <div class="content">
            <p>Dear {member_name},</p>
            
            <p>We\'re excited to announce a new session has been added to our upcoming events!</p>
            
            <div class="session-details">
                <h3>📅 {session_title}</h3>
                <p><strong>Event:</strong> {event_title}</p>
                <p><strong>Date & Time:</strong> {session_date} at {session_time}</p>
                <p><strong>Location:</strong> {session_location}</p>
                <p><strong>Instructor:</strong> {instructor_name}</p>
                <p><strong>Available Spots:</strong> {available_spots} remaining</p>
                {session_description}
            </div>
            
            <p><strong>Why you should attend:</strong></p>
            <ul>
                <li>Learn from experienced instructors</li>
                <li>Connect with fellow community members</li>
                <li>Gain valuable knowledge and skills</li>
                <li>Be part of our growing community</li>
            </ul>
            
            <p>Spaces are limited, so register early to secure your spot!</p>
            
            <a href="{registration_url}" class="button">Register Now</a>
            
            <p>We hope to see you there!</p>
            
            <p>Best regards,<br>
            {organization_name}</p>
        </div>
    </div>
</body>
</html>',
        'template_category' => 'announcement',
        'is_birthday_template' => 0
    ],

    [
        'template_name' => 'Session Update Notification',
        'subject' => 'Important Update: {session_title}',
        'content' => '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Session Update</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .update-box { background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .session-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #3498db; }
        .button { display: inline-block; background: #3498db; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📢 Session Update</h1>
            <p>Important information about your registered session</p>
        </div>
        <div class="content">
            <p>Dear {member_name},</p>

            <div class="update-box">
                <strong>📝 Update Notice:</strong> There has been an important update to your registered session.
            </div>

            <div class="session-details">
                <h3>📅 {session_title}</h3>
                <p><strong>Update Details:</strong> {update_message}</p>
                <p><strong>Date & Time:</strong> {session_date} at {session_time}</p>
                <p><strong>Location:</strong> {session_location}</p>
                <p><strong>Instructor:</strong> {instructor_name}</p>
            </div>

            <p>Please review the updated information carefully. If you have any questions or concerns about these changes, please contact us.</p>

            <a href="{my_sessions_url}" class="button">View Session Details</a>

            <p>Thank you for your understanding.</p>

            <p>Best regards,<br>
            {organization_name}</p>
        </div>
    </div>
</body>
</html>',
        'template_category' => 'announcement',
        'is_birthday_template' => 0
    ],

    [
        'template_name' => 'Session Cancellation Notice',
        'subject' => 'Session Cancelled: {session_title}',
        'content' => '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Session Cancellation</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .cancellation-box { background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .session-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #e74c3c; }
        .button { display: inline-block; background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>❌ Session Cancelled</h1>
            <p>We apologize for any inconvenience</p>
        </div>
        <div class="content">
            <p>Dear {member_name},</p>

            <div class="cancellation-box">
                <strong>⚠️ Cancellation Notice:</strong> Unfortunately, we need to cancel the following session.
            </div>

            <div class="session-details">
                <h3>📅 {session_title}</h3>
                <p><strong>Originally scheduled:</strong> {session_date} at {session_time}</p>
                <p><strong>Location:</strong> {session_location}</p>
                <p><strong>Reason:</strong> {cancellation_reason}</p>
            </div>

            <p>We sincerely apologize for this cancellation and any inconvenience it may cause. {alternative_options}</p>

            <p><strong>What happens next:</strong></p>
            <ul>
                <li>Your registration has been automatically cancelled</li>
                <li>You will be notified if a replacement session is scheduled</li>
                <li>You can browse other available sessions</li>
            </ul>

            <a href="{browse_sessions_url}" class="button">Browse Other Sessions</a>

            <p>Thank you for your understanding.</p>

            <p>Best regards,<br>
            {organization_name}</p>
        </div>
    </div>
</body>
</html>',
        'template_category' => 'announcement',
        'is_birthday_template' => 0
    ],

    [
        'template_name' => 'Admin: New Session Registration',
        'subject' => 'New Registration: {member_name} for {session_title}',
        'content' => '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>New Session Registration</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .registration-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #8e44ad; }
        .stats-box { background: #e8f5e8; border: 1px solid #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .button { display: inline-block; background: #8e44ad; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>👤 New Registration</h1>
            <p>A member has registered for a session</p>
        </div>
        <div class="content">
            <p>Dear Administrator,</p>

            <p>A new registration has been received for one of your sessions:</p>

            <div class="registration-details">
                <h3>📅 {session_title}</h3>
                <p><strong>Member:</strong> {member_name} ({member_email})</p>
                <p><strong>Registration Date:</strong> {registration_date}</p>
                <p><strong>Session Date:</strong> {session_date} at {session_time}</p>
                <p><strong>Location:</strong> {session_location}</p>
            </div>

            <div class="stats-box">
                <strong>📊 Current Registration Status:</strong><br>
                Registered: {current_registrations} / {max_attendees}<br>
                Available spots: {available_spots}
            </div>

            <a href="{admin_session_url}" class="button">Manage Session</a>

            <p>Best regards,<br>
            Church Management System</p>
        </div>
    </div>
</body>
</html>',
        'template_category' => 'general',
        'is_birthday_template' => 0
    ]
];

// Insert templates into database
$successCount = 0;
$errorCount = 0;

foreach ($sessionTemplates as $template) {
    try {
        // Check if template already exists
        $stmt = $pdo->prepare("SELECT id FROM email_templates WHERE template_name = ?");
        $stmt->execute([$template['template_name']]);
        
        if ($stmt->fetch()) {
            echo "<p style='color: orange;'>⚠️ Template '{$template['template_name']}' already exists - skipping</p>\n";
            continue;
        }
        
        // Insert new template
        $stmt = $pdo->prepare("
            INSERT INTO email_templates (template_name, subject, content, template_category, is_birthday_template, created_at) 
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $template['template_name'],
            $template['subject'],
            $template['content'],
            $template['template_category'],
            $template['is_birthday_template']
        ]);
        
        echo "<p style='color: green;'>✅ Created template: {$template['template_name']}</p>\n";
        $successCount++;
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ Error creating template '{$template['template_name']}': " . $e->getMessage() . "</p>\n";
        $errorCount++;
    }
}

echo "<hr>\n";
echo "<h2>📊 Summary</h2>\n";
echo "<p>✅ Successfully created: <strong>$successCount</strong> templates</p>\n";
echo "<p>❌ Errors: <strong>$errorCount</strong> templates</p>\n";

if ($successCount > 0) {
    echo "<p style='color: green; font-weight: bold;'>🎉 Session email templates setup complete!</p>\n";
    echo "<p>You can now view and manage these templates in the admin panel under Email Templates.</p>\n";
}

?>
