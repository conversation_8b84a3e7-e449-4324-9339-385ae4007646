<?php
require_once 'config.php';

echo "<h1>🖼️ Event 35 Banner Check</h1>\n";

// Check for banner files for event 35
$stmt = $pdo->prepare("
    SELECT id, file_name, file_path, file_type, is_header_banner, file_category
    FROM event_files 
    WHERE event_id = 35 
    ORDER BY is_header_banner DESC, id ASC
");
$stmt->execute();
$files = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h2>📁 All Files for Event 35:</h2>\n";
if (empty($files)) {
    echo "<p>No files found for event 35</p>\n";
} else {
    echo "<table border='1' cellpadding='5'>\n";
    echo "<tr><th>ID</th><th>File Name</th><th>File Path</th><th>Type</th><th>Is Banner</th><th>Category</th><th>Exists</th></tr>\n";
    
    foreach ($files as $file) {
        $exists = file_exists($file['file_path']) ? '✅' : '❌';
        $isBanner = $file['is_header_banner'] ? '🖼️ YES' : 'No';
        
        echo "<tr>";
        echo "<td>{$file['id']}</td>";
        echo "<td>{$file['file_name']}</td>";
        echo "<td>{$file['file_path']}</td>";
        echo "<td>{$file['file_type']}</td>";
        echo "<td>$isBanner</td>";
        echo "<td>{$file['file_category']}</td>";
        echo "<td>$exists</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
}

// Check event details
echo "<h2>📋 Event 35 Details:</h2>\n";
$stmt = $pdo->prepare("SELECT id, title, description, image_path FROM events WHERE id = 35");
$stmt->execute();
$event = $stmt->fetch(PDO::FETCH_ASSOC);

if ($event) {
    echo "<table border='1' cellpadding='5'>\n";
    echo "<tr><th>Field</th><th>Value</th></tr>\n";
    echo "<tr><td>ID</td><td>{$event['id']}</td></tr>\n";
    echo "<tr><td>Title</td><td>{$event['title']}</td></tr>\n";
    echo "<tr><td>Description</td><td>" . htmlspecialchars($event['description']) . "</td></tr>\n";
    echo "<tr><td>Image Path</td><td>{$event['image_path']}</td></tr>\n";
    echo "</table>\n";
}

// Test creating a banner file entry
echo "<h2>🧪 Banner Test</h2>\n";
echo "<p>If no banner exists, you can create one by:</p>\n";
echo "<ol>\n";
echo "<li>Upload an image file to uploads/events/banners/</li>\n";
echo "<li>Add a record to event_files table with is_header_banner = 1</li>\n";
echo "<li>Or use the admin panel to upload event materials</li>\n";
echo "</ol>\n";

// Check if there are any image files in the uploads directory that could be used as banners
echo "<h2>🖼️ Available Image Files:</h2>\n";
$imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
$uploadsDir = 'uploads/events/';

if (is_dir($uploadsDir)) {
    $files = scandir($uploadsDir);
    $imageFiles = [];
    
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..') {
            $ext = strtolower(pathinfo($file, PATHINFO_EXTENSION));
            if (in_array($ext, $imageExtensions)) {
                $imageFiles[] = $file;
            }
        }
    }
    
    if (!empty($imageFiles)) {
        echo "<p>Found " . count($imageFiles) . " image files that could be used as banners:</p>\n";
        echo "<ul>\n";
        foreach ($imageFiles as $file) {
            echo "<li>$file</li>\n";
        }
        echo "</ul>\n";
    } else {
        echo "<p>No image files found in uploads/events/</p>\n";
    }
} else {
    echo "<p>uploads/events/ directory not found</p>\n";
}

?>

<style>
table {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
}

th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
}

.banner-yes {
    background-color: #d4edda;
    font-weight: bold;
}
</style>
