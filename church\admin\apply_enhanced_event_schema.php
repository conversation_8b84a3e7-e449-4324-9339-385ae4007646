<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

$message = '';
$error = '';

// Apply schema updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['apply_schema'])) {
    try {
        // Read and execute the enhanced event management SQL
        $sql_file = __DIR__ . '/sql/enhanced_event_management.sql';
        
        if (file_exists($sql_file)) {
            $sql_content = file_get_contents($sql_file);
            
            // Split SQL statements and execute them
            $statements = array_filter(array_map('trim', explode(';', $sql_content)));
            
            $executed = 0;
            $errors = [];
            
            foreach ($statements as $statement) {
                if (!empty($statement) && !preg_match('/^\s*--/', $statement)) {
                    try {
                        $pdo->exec($statement);
                        $executed++;
                    } catch (PDOException $e) {
                        // Log error but continue with other statements
                        $errors[] = "Statement error: " . $e->getMessage();
                        error_log("SQL Statement Error: " . $e->getMessage() . " | Statement: " . substr($statement, 0, 100));
                    }
                }
            }
            
            if (empty($errors)) {
                $message = "✅ Enhanced event management schema applied successfully! Executed $executed statements.";
            } else {
                $message = "⚠️ Schema partially applied. Executed $executed statements with " . count($errors) . " errors.";
                $error = implode('<br>', $errors);
            }
            
        } else {
            $error = "❌ SQL file not found: $sql_file";
        }
        
    } catch (Exception $e) {
        $error = "❌ Error applying schema: " . $e->getMessage();
        error_log("Schema application error: " . $e->getMessage());
    }
}

// Check current schema status
$schema_status = [];
try {
    // Check if new columns exist
    $stmt = $pdo->query("DESCRIBE events");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $required_columns = [
        'status', 'allow_late_registration', 'late_registration_cutoff_hours',
        'cancellation_reason', 'cancelled_at', 'cancelled_by',
        'archived_at', 'archived_by', 'is_recurring', 'recurrence_type'
    ];
    
    foreach ($required_columns as $col) {
        $schema_status['events_' . $col] = in_array($col, $columns);
    }
    
    // Check if new tables exist
    $tables_to_check = [
        'event_status_history',
        'recurring_event_instances', 
        'event_settings',
        'backup_configurations',
        'backup_history'
    ];
    
    foreach ($tables_to_check as $table) {
        try {
            $stmt = $pdo->query("SELECT 1 FROM $table LIMIT 1");
            $schema_status['table_' . $table] = true;
        } catch (PDOException $e) {
            $schema_status['table_' . $table] = false;
        }
    }
    
} catch (Exception $e) {
    error_log("Error checking schema status: " . $e->getMessage());
}

// Page title and header info
$page_title = 'Apply Enhanced Event Management Schema';
$page_header = 'Enhanced Event Management Schema';
$page_description = 'Apply database schema updates for enhanced event management features';

// Include header
include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-database-gear"></i> Enhanced Event Management Schema
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="events.php" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Events
        </a>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo $error; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Schema Status -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-list-check"></i> Current Schema Status
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>Events Table Enhancements</h6>
                <ul class="list-unstyled">
                    <?php foreach ($schema_status as $key => $status): ?>
                        <?php if (strpos($key, 'events_') === 0): ?>
                            <li>
                                <?php if ($status): ?>
                                    <i class="bi bi-check-circle text-success"></i>
                                <?php else: ?>
                                    <i class="bi bi-x-circle text-danger"></i>
                                <?php endif; ?>
                                <?php echo str_replace('events_', '', $key); ?>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>New Tables</h6>
                <ul class="list-unstyled">
                    <?php foreach ($schema_status as $key => $status): ?>
                        <?php if (strpos($key, 'table_') === 0): ?>
                            <li>
                                <?php if ($status): ?>
                                    <i class="bi bi-check-circle text-success"></i>
                                <?php else: ?>
                                    <i class="bi bi-x-circle text-danger"></i>
                                <?php endif; ?>
                                <?php echo str_replace('table_', '', $key); ?>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Apply Schema -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-gear"></i> Apply Schema Updates
        </h5>
    </div>
    <div class="card-body">
        <p>This will apply the enhanced event management schema updates including:</p>
        <ul>
            <li><strong>Event Status Management</strong>: Add comprehensive status tracking (draft, published, cancelled, completed, archived)</li>
            <li><strong>Late Registration Controls</strong>: Add settings to control registration after event start time</li>
            <li><strong>Recurring Events</strong>: Add support for recurring event functionality</li>
            <li><strong>Backup System</strong>: Add database backup management tables</li>
            <li><strong>Status History</strong>: Track all status changes with audit trail</li>
        </ul>
        
        <div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle"></i>
            <strong>Important:</strong> This operation will modify your database structure. 
            It's recommended to backup your database before proceeding.
        </div>
        
        <form method="POST">
            <button type="submit" name="apply_schema" class="btn btn-primary">
                <i class="bi bi-database-gear"></i> Apply Schema Updates
            </button>
        </form>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
