<?php
// Test recurring event toggle functionality
require_once '../config.php';

echo "<h2>Testing Recurring Event Toggle Functionality</h2>";

try {
    // Test 1: Find an existing recurring event to test with
    echo "<h3>Test 1: Find Existing Recurring Event</h3>";
    
    $stmt = $pdo->query("
        SELECT id, title, is_recurring, recurrence_type, recurrence_interval, 
               recurrence_count, recurrence_end_date
        FROM events 
        WHERE is_recurring = 1 
        LIMIT 1
    ");
    $recurringEvent = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($recurringEvent) {
        echo "<p style='color: green;'>✅ Found recurring event to test with:</p>";
        echo "<ul>";
        echo "<li><strong>ID:</strong> " . $recurringEvent['id'] . "</li>";
        echo "<li><strong>Title:</strong> " . htmlspecialchars($recurringEvent['title']) . "</li>";
        echo "<li><strong>Pattern:</strong> " . ucfirst($recurringEvent['recurrence_type']) . "</li>";
        echo "<li><strong>Interval:</strong> Every " . $recurringEvent['recurrence_interval'] . " " . $recurringEvent['recurrence_type'] . "(s)</li>";
        echo "<li><strong>Count:</strong> " . $recurringEvent['recurrence_count'] . " occurrences</li>";
        echo "</ul>";
        
        $testEventId = $recurringEvent['id'];
    } else {
        echo "<p style='color: orange;'>⚠️ No recurring events found. Creating a test event...</p>";
        
        // Create a test recurring event
        $stmt = $pdo->prepare("
            INSERT INTO events (title, description, event_date, location, created_by, is_active, 
                              is_recurring, recurrence_type, recurrence_interval, recurrence_count)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            'Test Recurring Toggle Event',
            'Test event for toggle functionality',
            date('Y-m-d H:i:s', strtotime('+1 week')),
            'Test Location',
            1, // admin_id
            1, // is_active
            1, // is_recurring
            'weekly', // recurrence_type
            1, // recurrence_interval
            3  // recurrence_count
        ]);
        
        $testEventId = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ Created test recurring event with ID: $testEventId</p>";
    }
    
    // Test 2: Simulate toggling recurring OFF
    echo "<h3>Test 2: Toggle Recurring OFF</h3>";
    
    // Get current status
    $stmt = $pdo->prepare("SELECT is_recurring FROM events WHERE id = ?");
    $stmt->execute([$testEventId]);
    $currentStatus = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Current Status:</strong> " . ($currentStatus['is_recurring'] ? 'Recurring' : 'Not Recurring') . "</p>";
    
    if ($currentStatus['is_recurring']) {
        // Simulate the update query that would happen when unchecking the recurring checkbox
        $stmt = $pdo->prepare("
            UPDATE events SET is_recurring = ?, recurrence_type = ?, recurrence_interval = ?, 
                            recurrence_count = ?, recurrence_end_date = ?
            WHERE id = ?
        ");
        
        $stmt->execute([
            0,    // is_recurring = false
            null, // recurrence_type = null
            null, // recurrence_interval = null
            null, // recurrence_count = null
            null, // recurrence_end_date = null
            $testEventId
        ]);
        
        echo "<p style='color: green;'>✅ Successfully toggled recurring OFF</p>";
        
        // Verify the change
        $stmt = $pdo->prepare("
            SELECT is_recurring, recurrence_type, recurrence_interval, recurrence_count 
            FROM events WHERE id = ?
        ");
        $stmt->execute([$testEventId]);
        $updatedEvent = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<p><strong>After Toggle OFF:</strong></p>";
        echo "<ul>";
        echo "<li>is_recurring: " . ($updatedEvent['is_recurring'] ? 'Yes' : 'No') . "</li>";
        echo "<li>recurrence_type: " . ($updatedEvent['recurrence_type'] ?: 'NULL') . "</li>";
        echo "<li>recurrence_interval: " . ($updatedEvent['recurrence_interval'] ?: 'NULL') . "</li>";
        echo "<li>recurrence_count: " . ($updatedEvent['recurrence_count'] ?: 'NULL') . "</li>";
        echo "</ul>";
        
        if (!$updatedEvent['is_recurring'] && !$updatedEvent['recurrence_type']) {
            echo "<p style='color: green;'>✅ Recurring status successfully disabled and fields cleared</p>";
        } else {
            echo "<p style='color: red;'>❌ Recurring status not properly disabled</p>";
        }
    }
    
    // Test 3: Simulate toggling recurring ON
    echo "<h3>Test 3: Toggle Recurring ON</h3>";
    
    // Now toggle it back on with new settings
    $stmt = $pdo->prepare("
        UPDATE events SET is_recurring = ?, recurrence_type = ?, recurrence_interval = ?, 
                        recurrence_count = ?, recurrence_end_date = ?
        WHERE id = ?
    ");
    
    $stmt->execute([
        1,        // is_recurring = true
        'monthly', // recurrence_type = monthly
        2,        // recurrence_interval = every 2 months
        6,        // recurrence_count = 6 times
        null,     // recurrence_end_date = null
        $testEventId
    ]);
    
    echo "<p style='color: green;'>✅ Successfully toggled recurring ON with new pattern</p>";
    
    // Verify the change
    $stmt = $pdo->prepare("
        SELECT is_recurring, recurrence_type, recurrence_interval, recurrence_count 
        FROM events WHERE id = ?
    ");
    $stmt->execute([$testEventId]);
    $finalEvent = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p><strong>After Toggle ON:</strong></p>";
    echo "<ul>";
    echo "<li>is_recurring: " . ($finalEvent['is_recurring'] ? 'Yes' : 'No') . "</li>";
    echo "<li>recurrence_type: " . ($finalEvent['recurrence_type'] ?: 'NULL') . "</li>";
    echo "<li>recurrence_interval: " . ($finalEvent['recurrence_interval'] ?: 'NULL') . "</li>";
    echo "<li>recurrence_count: " . ($finalEvent['recurrence_count'] ?: 'NULL') . "</li>";
    echo "</ul>";
    
    if ($finalEvent['is_recurring'] && $finalEvent['recurrence_type'] === 'monthly' && $finalEvent['recurrence_interval'] == 2) {
        echo "<p style='color: green;'>✅ Recurring status successfully enabled with new pattern</p>";
    } else {
        echo "<p style='color: red;'>❌ Recurring status not properly enabled</p>";
    }
    
    // Test 4: Test the admin interface display
    echo "<h3>Test 4: Admin Interface Display</h3>";
    
    echo "<p><strong>How this event would appear in the admin interface:</strong></p>";
    
    $event = $finalEvent;
    $event['title'] = 'Test Recurring Toggle Event';
    
    echo "<div style='border: 1px solid #ddd; padding: 15px; background-color: #f9f9f9; margin: 10px 0;'>";
    echo "<strong>" . htmlspecialchars($event['title']) . "</strong> ";
    
    if ($event['is_recurring']) {
        echo "<span style='background-color: #d1ecf1; color: #0c5460; padding: 2px 8px; border-radius: 4px; font-size: 12px;'>";
        echo "<i>🔄</i> Recurring";
        echo "</span>";
    }
    
    echo "<br>";
    
    if ($event['is_recurring']) {
        echo "<small style='color: #17a2b8;'>";
        echo "<i>🔄</i> Repeats " . ucfirst($event['recurrence_type']);
        if ($event['recurrence_interval'] > 1) {
            $unit = $event['recurrence_type'] === 'daily' ? 'days' : 
                   ($event['recurrence_type'] === 'weekly' ? 'weeks' : 
                   ($event['recurrence_type'] === 'monthly' ? 'months' : 'years'));
            echo " (every " . $event['recurrence_interval'] . " $unit)";
        }
        echo " - " . $event['recurrence_count'] . " occurrences";
        echo " <span style='background-color: #f8f9fa; color: #6c757d; padding: 1px 4px; border-radius: 2px; font-size: 10px;'>";
        echo "<i>⚙️</i> Editable";
        echo "</span>";
        echo "</small>";
    }
    echo "</div>";
    
    // Test 5: User Interface Impact
    echo "<h3>Test 5: User Interface Impact</h3>";
    
    echo "<div style='padding: 15px; border: 1px solid #ddd; background-color: #f0f8ff;'>";
    echo "<h4>✅ Toggle Functionality Benefits</h4>";
    echo "<ul>";
    echo "<li><strong>✅ Flexible Management:</strong> Admins can easily switch events between recurring and non-recurring</li>";
    echo "<li><strong>✅ Same Interface:</strong> Uses the same checkbox and form fields for both directions</li>";
    echo "<li><strong>✅ Clean Transitions:</strong> Properly clears recurring data when disabled</li>";
    echo "<li><strong>✅ Pattern Updates:</strong> Can change recurring patterns by toggling off and on with new settings</li>";
    echo "<li><strong>✅ Visual Feedback:</strong> Clear indicators show current recurring status</li>";
    echo "<li><strong>✅ Data Integrity:</strong> Maintains database consistency during status changes</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test 6: Workflow Examples
    echo "<h3>Test 6: Common Admin Workflows</h3>";
    
    echo "<div style='padding: 15px; border: 1px solid #e7f3ff; background-color: #f8fcff;'>";
    echo "<h4>📋 Common Use Cases</h4>";
    
    echo "<p><strong>Scenario 1: Make Regular Event Recurring</strong></p>";
    echo "<ol>";
    echo "<li>Admin edits existing single event</li>";
    echo "<li>Checks 'Make this a recurring event' checkbox</li>";
    echo "<li>Sets pattern (e.g., Weekly, 10 occurrences)</li>";
    echo "<li>Saves - event becomes recurring</li>";
    echo "</ol>";
    
    echo "<p><strong>Scenario 2: Stop Recurring Series</strong></p>";
    echo "<ol>";
    echo "<li>Admin edits recurring event</li>";
    echo "<li>Unchecks 'Make this a recurring event' checkbox</li>";
    echo "<li>Saves - event becomes single occurrence</li>";
    echo "<li>Recurring pattern is cleared</li>";
    echo "</ol>";
    
    echo "<p><strong>Scenario 3: Change Recurring Pattern</strong></p>";
    echo "<ol>";
    echo "<li>Admin edits recurring event</li>";
    echo "<li>Modifies pattern (e.g., Weekly → Monthly)</li>";
    echo "<li>Saves - new pattern is applied</li>";
    echo "<li>Old instances are updated</li>";
    echo "</ol>";
    echo "</div>";
    
    // Cleanup test event if we created it
    if (!$recurringEvent) {
        $stmt = $pdo->prepare("DELETE FROM events WHERE id = ?");
        $stmt->execute([$testEventId]);
        echo "<p><small>🧹 Cleaned up test event</small></p>";
    }
    
    echo "<h3>✅ Recurring Toggle Functionality Complete!</h3>";
    echo "<p><strong>Admins can now easily toggle events between recurring and non-recurring using the same interface.</strong></p>";
    
} catch (Exception $e) {
    echo "<h3>❌ Error during testing: " . htmlspecialchars($e->getMessage()) . "</h3>";
    error_log("Recurring toggle test error: " . $e->getMessage());
}
?>
