<?php
/**
 * Session Notification Manager
 * 
 * Handles all session-related notifications including email and in-app notifications
 */

require_once __DIR__ . '/../includes/email_functions.php';
require_once __DIR__ . '/../includes/notification_functions.php';

class SessionNotificationManager {
    private $pdo;
    private $logger;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->logger = new SessionNotificationLogger($pdo);
    }
    
    /**
     * Send session registration confirmation
     */
    public function sendRegistrationConfirmation($sessionId, $memberId, $guestEmail = null, $guestName = null) {
        try {
            $sessionData = $this->getSessionData($sessionId);
            if (!$sessionData) {
                throw new Exception("Session not found: $sessionId");
            }
            
            if ($memberId) {
                $memberData = $this->getMemberData($memberId);
                $recipientEmail = $memberData['email'];
                $recipientName = $memberData['first_name'] . ' ' . $memberData['last_name'];
            } else {
                $recipientEmail = $guestEmail;
                $recipientName = $guestName;
            }
            
            // Get email template
            $template = $this->getEmailTemplate('Session Registration Confirmation');
            if (!$template) {
                throw new Exception("Registration confirmation template not found");
            }
            
            // Prepare template variables
            $variables = $this->prepareSessionVariables($sessionData, $recipientName);
            
            // Send email
            $emailResult = $this->sendTemplatedEmail(
                $recipientEmail,
                $recipientName,
                $template,
                $variables,
                'session_registration'
            );
            
            // Create in-app notification for members
            if ($memberId) {
                $this->createInAppNotification(
                    $memberId,
                    'Session Registration Confirmed',
                    "You have successfully registered for '{$sessionData['session_title']}'",
                    'event',
                    "/user/my_sessions.php",
                    'normal'
                );
            }
            
            $this->logger->logNotification('registration_confirmation', $sessionId, $memberId, $emailResult['success']);
            
            return $emailResult;
            
        } catch (Exception $e) {
            $this->logger->logError('registration_confirmation', $sessionId, $memberId, $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Send session reminder (24 hours before)
     */
    public function sendSessionReminder($sessionId, $memberId) {
        try {
            $sessionData = $this->getSessionData($sessionId);
            $memberData = $this->getMemberData($memberId);
            
            if (!$sessionData || !$memberData) {
                throw new Exception("Session or member data not found");
            }
            
            // Check if session is within 24-48 hours
            $sessionTime = strtotime($sessionData['start_datetime']);
            $currentTime = time();
            $hoursUntilSession = ($sessionTime - $currentTime) / 3600;
            
            if ($hoursUntilSession < 12 || $hoursUntilSession > 48) {
                // Not in the reminder window
                return ['success' => false, 'message' => 'Not in reminder window'];
            }
            
            $template = $this->getEmailTemplate('Session Reminder - 24 Hours');
            if (!$template) {
                throw new Exception("Session reminder template not found");
            }
            
            $recipientName = $memberData['first_name'] . ' ' . $memberData['last_name'];
            $variables = $this->prepareSessionVariables($sessionData, $recipientName);
            
            $emailResult = $this->sendTemplatedEmail(
                $memberData['email'],
                $recipientName,
                $template,
                $variables,
                'session_reminder'
            );
            
            // Create in-app notification
            $this->createInAppNotification(
                $memberId,
                'Session Reminder',
                "Don't forget: '{$sessionData['session_title']}' is tomorrow at " . date('g:i A', strtotime($sessionData['start_datetime'])),
                'reminder',
                "/user/my_sessions.php",
                'high'
            );
            
            $this->logger->logNotification('session_reminder', $sessionId, $memberId, $emailResult['success']);
            
            return $emailResult;
            
        } catch (Exception $e) {
            $this->logger->logError('session_reminder', $sessionId, $memberId, $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Notify users about new session availability
     */
    public function notifyNewSession($sessionId, $targetAudience = 'all') {
        try {
            $sessionData = $this->getSessionData($sessionId);
            if (!$sessionData) {
                throw new Exception("Session not found: $sessionId");
            }
            
            // Get recipients based on target audience
            $recipients = $this->getNotificationRecipients($targetAudience, $sessionData['event_id']);
            
            $template = $this->getEmailTemplate('New Session Available');
            if (!$template) {
                throw new Exception("New session template not found");
            }
            
            $successCount = 0;
            $errorCount = 0;
            
            foreach ($recipients as $recipient) {
                try {
                    $variables = $this->prepareSessionVariables($sessionData, $recipient['name']);
                    $variables['{available_spots}'] = $this->getAvailableSpots($sessionId);
                    $variables['{registration_url}'] = $this->getRegistrationUrl($sessionId);
                    
                    $emailResult = $this->sendTemplatedEmail(
                        $recipient['email'],
                        $recipient['name'],
                        $template,
                        $variables,
                        'new_session'
                    );
                    
                    if ($emailResult['success']) {
                        $successCount++;
                        
                        // Create in-app notification
                        if (isset($recipient['member_id'])) {
                            $this->createInAppNotification(
                                $recipient['member_id'],
                                'New Session Available',
                                "New session added: '{$sessionData['session_title']}'",
                                'announcement',
                                "/user/event_sessions.php?event_id={$sessionData['event_id']}",
                                'normal'
                            );
                        }
                    } else {
                        $errorCount++;
                    }
                    
                } catch (Exception $e) {
                    $errorCount++;
                    $this->logger->logError('new_session', $sessionId, $recipient['member_id'] ?? null, $e->getMessage());
                }
            }
            
            $this->logger->logBulkNotification('new_session', $sessionId, $successCount, $errorCount);
            
            return [
                'success' => true,
                'sent' => $successCount,
                'errors' => $errorCount,
                'total' => count($recipients)
            ];
            
        } catch (Exception $e) {
            $this->logger->logError('new_session', $sessionId, null, $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Send session update notification
     */
    public function sendSessionUpdate($sessionId, $updateMessage, $updateType = 'general') {
        try {
            $sessionData = $this->getSessionData($sessionId);
            if (!$sessionData) {
                throw new Exception("Session not found: $sessionId");
            }
            
            // Get all registered attendees
            $attendees = $this->getSessionAttendees($sessionId);
            
            $template = $this->getEmailTemplate('Session Update Notification');
            if (!$template) {
                throw new Exception("Session update template not found");
            }
            
            $successCount = 0;
            $errorCount = 0;
            
            foreach ($attendees as $attendee) {
                try {
                    $variables = $this->prepareSessionVariables($sessionData, $attendee['name']);
                    $variables['{update_message}'] = $updateMessage;
                    
                    $emailResult = $this->sendTemplatedEmail(
                        $attendee['email'],
                        $attendee['name'],
                        $template,
                        $variables,
                        'session_update'
                    );
                    
                    if ($emailResult['success']) {
                        $successCount++;
                        
                        // Create in-app notification
                        if ($attendee['member_id']) {
                            $this->createInAppNotification(
                                $attendee['member_id'],
                                'Session Update',
                                "Update for '{$sessionData['session_title']}': $updateMessage",
                                'announcement',
                                "/user/my_sessions.php",
                                'high'
                            );
                        }
                    } else {
                        $errorCount++;
                    }
                    
                } catch (Exception $e) {
                    $errorCount++;
                    $this->logger->logError('session_update', $sessionId, $attendee['member_id'] ?? null, $e->getMessage());
                }
            }
            
            $this->logger->logBulkNotification('session_update', $sessionId, $successCount, $errorCount);
            
            return [
                'success' => true,
                'sent' => $successCount,
                'errors' => $errorCount,
                'total' => count($attendees)
            ];
            
        } catch (Exception $e) {
            $this->logger->logError('session_update', $sessionId, null, $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Send session cancellation notification
     */
    public function sendSessionCancellation($sessionId, $cancellationReason, $alternativeOptions = '') {
        try {
            $sessionData = $this->getSessionData($sessionId);
            if (!$sessionData) {
                throw new Exception("Session not found: $sessionId");
            }
            
            // Get all registered attendees
            $attendees = $this->getSessionAttendees($sessionId);
            
            $template = $this->getEmailTemplate('Session Cancellation Notice');
            if (!$template) {
                throw new Exception("Session cancellation template not found");
            }
            
            $successCount = 0;
            $errorCount = 0;
            
            foreach ($attendees as $attendee) {
                try {
                    $variables = $this->prepareSessionVariables($sessionData, $attendee['name']);
                    $variables['{cancellation_reason}'] = $cancellationReason;
                    $variables['{alternative_options}'] = $alternativeOptions;
                    $variables['{browse_sessions_url}'] = $this->getBrowseSessionsUrl($sessionData['event_id']);
                    
                    $emailResult = $this->sendTemplatedEmail(
                        $attendee['email'],
                        $attendee['name'],
                        $template,
                        $variables,
                        'session_cancellation'
                    );
                    
                    if ($emailResult['success']) {
                        $successCount++;
                        
                        // Create in-app notification
                        if ($attendee['member_id']) {
                            $this->createInAppNotification(
                                $attendee['member_id'],
                                'Session Cancelled',
                                "'{$sessionData['session_title']}' has been cancelled. Reason: $cancellationReason",
                                'announcement',
                                "/user/event_sessions.php?event_id={$sessionData['event_id']}",
                                'urgent'
                            );
                        }
                    } else {
                        $errorCount++;
                    }
                    
                } catch (Exception $e) {
                    $errorCount++;
                    $this->logger->logError('session_cancellation', $sessionId, $attendee['member_id'] ?? null, $e->getMessage());
                }
            }
            
            $this->logger->logBulkNotification('session_cancellation', $sessionId, $successCount, $errorCount);
            
            return [
                'success' => true,
                'sent' => $successCount,
                'errors' => $errorCount,
                'total' => count($attendees)
            ];
            
        } catch (Exception $e) {
            $this->logger->logError('session_cancellation', $sessionId, null, $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Notify admin about new registration
     */
    public function notifyAdminNewRegistration($sessionId, $memberId, $guestName = null, $guestEmail = null) {
        try {
            $sessionData = $this->getSessionData($sessionId);
            if (!$sessionData) {
                throw new Exception("Session not found: $sessionId");
            }

            // Get admin recipients
            $admins = $this->getAdminRecipients();

            $template = $this->getEmailTemplate('Admin: New Session Registration');
            if (!$template) {
                throw new Exception("Admin registration template not found");
            }

            $memberName = $guestName;
            $memberEmail = $guestEmail;

            if ($memberId) {
                $memberData = $this->getMemberData($memberId);
                $memberName = $memberData['first_name'] . ' ' . $memberData['last_name'];
                $memberEmail = $memberData['email'];
            }

            $successCount = 0;

            foreach ($admins as $admin) {
                try {
                    $variables = $this->prepareSessionVariables($sessionData, $admin['name']);
                    $variables['{member_name}'] = $memberName;
                    $variables['{member_email}'] = $memberEmail;
                    $variables['{registration_date}'] = date('F j, Y g:i A');
                    $variables['{current_registrations}'] = $this->getCurrentRegistrations($sessionId);
                    $variables['{available_spots}'] = $this->getAvailableSpots($sessionId);
                    $variables['{admin_session_url}'] = $this->getAdminSessionUrl($sessionId);

                    $emailResult = $this->sendTemplatedEmail(
                        $admin['email'],
                        $admin['name'],
                        $template,
                        $variables,
                        'admin_notification'
                    );

                    if ($emailResult['success']) {
                        $successCount++;

                        // Create admin in-app notification
                        $this->createAdminNotification(
                            $admin['id'],
                            'New Session Registration',
                            "$memberName registered for '{$sessionData['session_title']}'",
                            'member_activity',
                            "/admin/event_sessions.php?event_id={$sessionData['event_id']}",
                            'normal'
                        );
                    }

                } catch (Exception $e) {
                    $this->logger->logError('admin_notification', $sessionId, $admin['id'], $e->getMessage());
                }
            }

            return ['success' => $successCount > 0, 'sent' => $successCount];

        } catch (Exception $e) {
            $this->logger->logError('admin_notification', $sessionId, null, $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    // Helper Methods

    private function getSessionData($sessionId) {
        $stmt = $this->pdo->prepare("
            SELECT es.*, e.title as event_title, e.location as event_location
            FROM event_sessions es
            JOIN events e ON es.event_id = e.id
            WHERE es.id = ?
        ");
        $stmt->execute([$sessionId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    private function getMemberData($memberId) {
        $stmt = $this->pdo->prepare("SELECT * FROM members WHERE id = ?");
        $stmt->execute([$memberId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    private function getEmailTemplate($templateName) {
        $stmt = $this->pdo->prepare("SELECT * FROM email_templates WHERE template_name = ?");
        $stmt->execute([$templateName]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    private function prepareSessionVariables($sessionData, $recipientName) {
        return [
            '{member_name}' => $recipientName,
            '{session_title}' => $sessionData['session_title'],
            '{session_description}' => $sessionData['session_description'] ? '<p><strong>Description:</strong> ' . $sessionData['session_description'] . '</p>' : '',
            '{event_title}' => $sessionData['event_title'],
            '{session_date}' => date('l, F j, Y', strtotime($sessionData['start_datetime'])),
            '{session_time}' => date('g:i A', strtotime($sessionData['start_datetime'])),
            '{session_location}' => $sessionData['location'] ?: $sessionData['event_location'],
            '{instructor_name}' => $sessionData['instructor_name'] ?: 'TBD',
            '{session_duration}' => $this->calculateDuration($sessionData['start_datetime'], $sessionData['end_datetime']),
            '{organization_name}' => $this->getOrganizationName(),
            '{my_sessions_url}' => $this->getBaseUrl() . '/user/my_sessions.php',
            '{max_attendees}' => $sessionData['max_attendees'] ?: 'Unlimited'
        ];
    }

    private function sendTemplatedEmail($recipientEmail, $recipientName, $template, $variables, $emailType) {
        // Replace placeholders in subject and content
        $subject = str_replace(array_keys($variables), array_values($variables), $template['subject']);
        $content = str_replace(array_keys($variables), array_values($variables), $template['content']);

        // Queue email for sending
        return $this->queueEmail(
            $recipientEmail,
            $recipientName,
            $subject,
            $content,
            $emailType,
            $template['id']
        );
    }

    private function queueEmail($recipientEmail, $recipientName, $subject, $body, $emailType, $templateId = null) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO email_queue (
                    recipient_email, recipient_name, subject, body,
                    email_type, template_id, priority, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, 5, 'pending', NOW())
            ");

            $stmt->execute([
                $recipientEmail,
                $recipientName,
                $subject,
                $body,
                $emailType,
                $templateId
            ]);

            return ['success' => true, 'message' => 'Email queued successfully'];

        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to queue email: ' . $e->getMessage()];
        }
    }

    private function createInAppNotification($recipientId, $title, $message, $type, $actionUrl = null, $priority = 'normal') {
        return createNotification($this->pdo, $recipientId, $title, $message, $type, null, 'system', $actionUrl, $priority);
    }

    private function createAdminNotification($adminId, $title, $message, $type, $actionUrl = null, $priority = 'normal') {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO admin_notifications (
                    recipient_id, title, message, notification_type,
                    action_url, priority, sender_type, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, 'system', NOW())
            ");

            return $stmt->execute([$adminId, $title, $message, $type, $actionUrl, $priority]);

        } catch (Exception $e) {
            error_log("Failed to create admin notification: " . $e->getMessage());
            return false;
        }
    }

    private function getSessionAttendees($sessionId) {
        $stmt = $this->pdo->prepare("
            SELECT
                sa.member_id,
                COALESCE(m.first_name || ' ' || m.last_name, sa.guest_name) as name,
                COALESCE(m.email, sa.guest_email) as email
            FROM session_attendance sa
            LEFT JOIN members m ON sa.member_id = m.id
            WHERE sa.session_id = ? AND sa.attendance_status = 'registered'
        ");
        $stmt->execute([$sessionId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    private function getNotificationRecipients($targetAudience, $eventId = null) {
        // For now, get all active members
        $stmt = $this->pdo->prepare("
            SELECT id as member_id, email,
                   COALESCE(first_name || ' ' || last_name, full_name) as name
            FROM members
            WHERE email IS NOT NULL AND email != ''
            ORDER BY id
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    private function getAdminRecipients() {
        $stmt = $this->pdo->prepare("
            SELECT id, email,
                   COALESCE(full_name, username) as name
            FROM admins
            WHERE email IS NOT NULL AND email != ''
            ORDER BY id
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    private function getCurrentRegistrations($sessionId) {
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) as count
            FROM session_attendance
            WHERE session_id = ? AND attendance_status = 'registered'
        ");
        $stmt->execute([$sessionId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'];
    }

    private function getAvailableSpots($sessionId) {
        $stmt = $this->pdo->prepare("
            SELECT es.max_attendees, COUNT(sa.id) as registered
            FROM event_sessions es
            LEFT JOIN session_attendance sa ON es.id = sa.session_id AND sa.attendance_status = 'registered'
            WHERE es.id = ?
            GROUP BY es.id, es.max_attendees
        ");
        $stmt->execute([$sessionId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$result['max_attendees']) {
            return 'Unlimited';
        }

        return max(0, $result['max_attendees'] - $result['registered']);
    }

    private function calculateDuration($startDateTime, $endDateTime) {
        $start = strtotime($startDateTime);
        $end = strtotime($endDateTime);
        $minutes = ($end - $start) / 60;

        if ($minutes < 60) {
            return $minutes . ' minutes';
        } else {
            $hours = floor($minutes / 60);
            $remainingMinutes = $minutes % 60;
            return $hours . ' hour' . ($hours > 1 ? 's' : '') .
                   ($remainingMinutes > 0 ? ' ' . $remainingMinutes . ' minutes' : '');
        }
    }

    private function getOrganizationName() {
        try {
            $stmt = $this->pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result ? $result['setting_value'] : 'Church Management System';
        } catch (Exception $e) {
            return 'Church Management System';
        }
    }

    private function getBaseUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $path = dirname($_SERVER['SCRIPT_NAME'] ?? '');
        return $protocol . '://' . $host . $path;
    }

    private function getRegistrationUrl($sessionId) {
        $sessionData = $this->getSessionData($sessionId);
        return $this->getBaseUrl() . "/user/event_sessions.php?event_id=" . $sessionData['event_id'];
    }

    private function getBrowseSessionsUrl($eventId) {
        return $this->getBaseUrl() . "/user/event_sessions.php?event_id=" . $eventId;
    }

    private function getAdminSessionUrl($sessionId) {
        $sessionData = $this->getSessionData($sessionId);
        return $this->getBaseUrl() . "/admin/event_sessions.php?event_id=" . $sessionData['event_id'];
    }
}
