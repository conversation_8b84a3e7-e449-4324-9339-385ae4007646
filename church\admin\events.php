<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Include event status manager
require_once '../classes/EventStatusManager.php';
require_once '../classes/RecurringEventManager.php';

// Initialize event status manager
$eventStatusManager = new EventStatusManager($pdo);
$recurringEventManager = new RecurringEventManager($pdo);

// Handle event status actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $eventId = (int)($_POST['event_id'] ?? 0);
    $reason = $_POST['reason'] ?? '';
    $adminId = $_SESSION['admin_id'];

    $response = ['success' => false, 'message' => ''];

    switch ($action) {
        case 'activate':
            $response = $eventStatusManager->activateEvent($eventId, $adminId, $reason);
            break;
        case 'deactivate':
            $response = $eventStatusManager->deactivateEvent($eventId, $adminId, $reason);
            break;
        case 'cancel':
            $response = $eventStatusManager->cancelEvent($eventId, $adminId, $reason);
            break;
        case 'archive':
            $response = $eventStatusManager->archiveEvent($eventId, $adminId, $reason);
            break;
        case 'update_late_registration':
            $allowLateReg = isset($_POST['allow_late_registration']) ? 1 : 0;
            $cutoffHours = (int)($_POST['late_registration_cutoff_hours'] ?? 0);
            $response = $eventStatusManager->updateLateRegistrationSettings($eventId, $allowLateReg, $cutoffHours, $adminId);
            break;
        case 'create_recurring':
            $recurrenceConfig = [
                'type' => $_POST['recurrence_type'] ?? 'weekly',
                'interval' => (int)($_POST['recurrence_interval'] ?? 1),
                'count' => (int)($_POST['recurrence_count'] ?? 10),
                'end_date' => $_POST['recurrence_end_date'] ?? null
            ];
            $response = $recurringEventManager->createRecurringInstances($eventId, $recurrenceConfig);
            break;
        case 'delete_recurring_series':
            $deleteInstances = isset($_POST['delete_instances']);
            $response = $recurringEventManager->deleteRecurringSeries($eventId, $deleteInstances);
            break;
    }

    if (isset($_POST['ajax'])) {
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    } else {
        $message = $response['message'];
        $message_type = $response['success'] ? 'success' : 'danger';
    }
}

// Include language system
require_once 'includes/language.php';

// Language manager is automatically initialized in language.php as global $lang

// Database connection - using the connection from config.php
$conn = $pdo;

// Helper function to calculate time elapsed
function time_elapsed_string($datetime) {
    $time = time() - $datetime;

    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' min';
    if ($time < 86400) return floor($time/3600) . ' hr';
    if ($time < 2592000) return floor($time/86400) . ' day' . (floor($time/86400) > 1 ? 's' : '');
    if ($time < 31536000) return floor($time/2592000) . ' month' . (floor($time/2592000) > 1 ? 's' : '');
    return floor($time/31536000) . ' year' . (floor($time/31536000) > 1 ? 's' : '');
}

$message = '';
$error = '';

// Function to handle promotional material uploads
function handleEventFileUploads($event_id, $uploaded_by) {
    global $conn;

    if (!isset($_FILES['event_files']) || empty($_FILES['event_files']['name'][0])) {
        return ['success' => true, 'message' => 'No files to upload'];
    }

    // Create upload directories using dynamic paths
    $base_upload_dir = admin_file_path('uploads/events/');
    $promotional_dir = $base_upload_dir . 'promotional/';
    $thumbnails_dir = $base_upload_dir . 'thumbnails/';

    foreach ([$base_upload_dir, $promotional_dir, $thumbnails_dir] as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }

    // Enhanced file type validation
    $allowed_types = [
        // Images
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif',
        // Documents
        'application/pdf'
    ];

    $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf'];
    $max_file_size = 15 * 1024 * 1024; // 15MB for promotional materials

    $uploaded_files = [];
    $errors = [];

    for ($i = 0; $i < count($_FILES['event_files']['name']); $i++) {
        if ($_FILES['event_files']['error'][$i] === UPLOAD_ERR_OK) {
            $file_name = $_FILES['event_files']['name'][$i];
            $file_tmp = $_FILES['event_files']['tmp_name'][$i];
            $file_size = $_FILES['event_files']['size'][$i];
            $file_type = $_FILES['event_files']['type'][$i];
            $file_extension = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

            // Validate file type and extension
            if (!in_array($file_type, $allowed_types) || !in_array($file_extension, $allowed_extensions)) {
                $errors[] = "Invalid file type for: $file_name";
                continue;
            }

            // Validate file size
            if ($file_size > $max_file_size) {
                $errors[] = "File too large: $file_name (max 15MB)";
                continue;
            }

            // Determine file category
            $file_category = in_array($file_extension, ['jpg', 'jpeg', 'png', 'gif']) ? 'promotional' : 'document';

            // Generate unique filename
            $unique_name = 'event_' . $event_id . '_' . time() . '_' . uniqid() . '.' . $file_extension;
            $target_dir = ($file_category === 'promotional') ? $promotional_dir : $base_upload_dir;
            $file_path = $target_dir . $unique_name;

            if (move_uploaded_file($file_tmp, $file_path)) {
                // Generate thumbnail for images
                $thumbnail_path = null;
                if ($file_category === 'promotional') {
                    $thumbnail_path = generateThumbnail($file_path, $thumbnails_dir, $unique_name);
                }

                // Save file info to database
                try {
                    $stmt = $conn->prepare("
                        INSERT INTO event_files (event_id, file_name, file_path, file_type, file_size,
                                               uploaded_by, file_category, display_order)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $event_id,
                        $file_name,
                        $file_path,
                        $file_type,
                        $file_size,
                        $uploaded_by,
                        $file_category,
                        $i // Use upload order as initial display order
                    ]);

                    $uploaded_files[] = [
                        'id' => $conn->lastInsertId(),
                        'name' => $file_name,
                        'category' => $file_category,
                        'thumbnail' => $thumbnail_path
                    ];
                } catch (PDOException $e) {
                    $errors[] = "Database error for $file_name: " . $e->getMessage();
                    // Clean up uploaded file if database insert fails
                    if (file_exists($file_path)) {
                        unlink($file_path);
                    }
                }
            } else {
                $errors[] = "Failed to upload: $file_name";
            }
        }
    }

    return [
        'success' => empty($errors),
        'uploaded_files' => $uploaded_files,
        'errors' => $errors,
        'message' => empty($errors) ? 'Files uploaded successfully' : 'Some files failed to upload'
    ];
}

// Function to generate thumbnails for images
function generateThumbnail($source_path, $thumbnail_dir, $filename) {
    $thumbnail_path = $thumbnail_dir . 'thumb_' . $filename;

    // Get image info
    $image_info = getimagesize($source_path);
    if (!$image_info) {
        return null;
    }

    $width = $image_info[0];
    $height = $image_info[1];
    $type = $image_info[2];

    // Calculate thumbnail dimensions (max 300x200)
    $thumb_width = 300;
    $thumb_height = 200;

    $ratio = min($thumb_width / $width, $thumb_height / $height);
    $new_width = intval($width * $ratio);
    $new_height = intval($height * $ratio);

    // Create source image
    switch ($type) {
        case IMAGETYPE_JPEG:
            $source = imagecreatefromjpeg($source_path);
            break;
        case IMAGETYPE_PNG:
            $source = imagecreatefrompng($source_path);
            break;
        case IMAGETYPE_GIF:
            $source = imagecreatefromgif($source_path);
            break;
        default:
            return null;
    }

    if (!$source) {
        return null;
    }

    // Create thumbnail
    $thumbnail = imagecreatetruecolor($new_width, $new_height);

    // Preserve transparency for PNG and GIF
    if ($type == IMAGETYPE_PNG || $type == IMAGETYPE_GIF) {
        imagealphablending($thumbnail, false);
        imagesavealpha($thumbnail, true);
        $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
        imagefilledrectangle($thumbnail, 0, 0, $new_width, $new_height, $transparent);
    }

    imagecopyresampled($thumbnail, $source, 0, 0, 0, 0, $new_width, $new_height, $width, $height);

    // Save thumbnail
    $success = false;
    switch ($type) {
        case IMAGETYPE_JPEG:
            $success = imagejpeg($thumbnail, $thumbnail_path, 85);
            break;
        case IMAGETYPE_PNG:
            $success = imagepng($thumbnail, $thumbnail_path);
            break;
        case IMAGETYPE_GIF:
            $success = imagegif($thumbnail, $thumbnail_path);
            break;
    }

    imagedestroy($source);
    imagedestroy($thumbnail);

    return $success ? $thumbnail_path : null;
}

// Create tables if they don't exist
try {
    $sql_file = __DIR__ . '/sql/events_tables.sql';
    if (file_exists($sql_file)) {
        $sql = file_get_contents($sql_file);
        if ($sql !== false && !empty(trim($sql))) {
            $conn->exec($sql);
        } else {
            error_log("Events SQL file is empty or could not be read: " . $sql_file);
        }
    } else {
        error_log("Events SQL file not found: " . $sql_file);
        // Create basic events table if SQL file doesn't exist
        $conn->exec("
            CREATE TABLE IF NOT EXISTS events (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                event_date DATETIME NOT NULL,
                location VARCHAR(255),
                max_attendees INT(11) DEFAULT NULL,
                category_id INT(11) DEFAULT NULL,
                created_by INT(11) NOT NULL,
                is_active TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");

        $conn->exec("
            CREATE TABLE IF NOT EXISTS event_files (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                event_id INT(11) NOT NULL,
                file_name VARCHAR(255) NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                file_type VARCHAR(100),
                file_size INT(11),
                uploaded_by INT(11) NOT NULL,
                upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ");

        $conn->exec("
            CREATE TABLE IF NOT EXISTS event_categories (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                color VARCHAR(7) DEFAULT '#007bff',
                is_active TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ");

        $conn->exec("
            CREATE TABLE IF NOT EXISTS event_rsvps (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                event_id INT(11) NOT NULL,
                member_id INT(11) NOT NULL,
                status ENUM('attending', 'not_attending', 'maybe') DEFAULT 'attending',
                guest_count INT(11) DEFAULT 0,
                notes TEXT,
                rsvp_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ");
    }
} catch (PDOException $e) {
    error_log("Error creating events tables: " . $e->getMessage());
} catch (Exception $e) {
    error_log("Error reading events SQL file: " . $e->getMessage());
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create_event':
                try {
                    // Debug logging
                    error_log("CREATE EVENT - Status: " . ($_POST['status'] ?? 'not set'));
                    error_log("CREATE EVENT - Is recurring: " . (isset($_POST['is_recurring']) ? 'yes' : 'no'));

                    // Check if this is a recurring event
                    $isRecurring = isset($_POST['is_recurring']) && $_POST['is_recurring'] === 'on';

                    $stmt = $conn->prepare("
                        INSERT INTO events (title, description, requirements, event_date, location,
                                          max_attendees, category_id, created_by, is_active, is_recurring,
                                          recurrence_type, recurrence_interval, recurrence_count, recurrence_end_date)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");

                    $stmt->execute([
                        $_POST['title'],
                        $_POST['description'],
                        !empty($_POST['requirements']) ? $_POST['requirements'] : null,
                        $_POST['start_datetime'], // Use start_datetime as event_date
                        $_POST['location'],
                        !empty($_POST['capacity']) ? (int)$_POST['capacity'] : null,
                        !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null,
                        $_SESSION['admin_id'],
                        ($_POST['status'] === 'published') ? 1 : 0, // Convert status to is_active
                        $isRecurring ? 1 : 0,
                        $isRecurring ? $_POST['recurrence_type'] : null,
                        $isRecurring ? (int)$_POST['recurrence_interval'] : null,
                        $isRecurring ? (int)$_POST['recurrence_count'] : null,
                        $isRecurring && !empty($_POST['recurrence_end_date']) ? $_POST['recurrence_end_date'] : null
                    ]);

                    $event_id = $conn->lastInsertId();

                    // Create recurring instances if this is a recurring event
                    if ($isRecurring) {
                        $recurringResult = $recurringEventManager->createRecurringInstances($event_id, [
                            'type' => $_POST['recurrence_type'],
                            'interval' => (int)$_POST['recurrence_interval'],
                            'count' => (int)$_POST['recurrence_count'],
                            'end_date' => !empty($_POST['recurrence_end_date']) ? $_POST['recurrence_end_date'] : null
                        ]);

                        if (!$recurringResult['success']) {
                            error_log("Failed to create recurring instances: " . $recurringResult['message']);
                        }
                    }

                    // Handle file uploads for Additional Documents (PDF)
                    $file_upload_result = handleEventFileUploads($event_id, $_SESSION['admin_id']);

                    $message = __('event_created_successfully');
                    if (!empty($file_upload_result['errors'])) {
                        $message .= " Note: Some file uploads failed: " . implode(', ', $file_upload_result['errors']);
                    } else if (!empty($file_upload_result['uploaded_files'])) {
                        $message .= " " . count($file_upload_result['uploaded_files']) . " file(s) uploaded successfully.";
                    }

                    // Set a flag to automatically open the edit modal for the new event
                    $auto_edit_event_id = $event_id;
                } catch (PDOException $e) {
                    $error = __('error_creating_event') . ": " . $e->getMessage();
                }
                break;
                
            case 'update_event':
                try {
                    // Debug logging
                    error_log("UPDATE EVENT - Status: " . ($_POST['status'] ?? 'not set'));
                    error_log("UPDATE EVENT - Is recurring: " . (isset($_POST['is_recurring']) ? 'yes' : 'no'));

                    // Check if this is a recurring event
                    $isRecurring = isset($_POST['is_recurring']) && $_POST['is_recurring'] === 'on';
                    $eventId = (int)$_POST['event_id'];

                    error_log("UPDATE EVENT - Event ID: $eventId");

                    // Get current event status to check if it was previously recurring
                    $stmt = $conn->prepare("SELECT is_recurring FROM events WHERE id = ?");
                    $stmt->execute([$eventId]);
                    $currentEvent = $stmt->fetch(PDO::FETCH_ASSOC);
                    $wasRecurring = $currentEvent && $currentEvent['is_recurring'];

                    $stmt = $conn->prepare("
                        UPDATE events SET title = ?, description = ?, requirements = ?, event_date = ?,
                               location = ?, max_attendees = ?, category_id = ?, is_active = ?, is_recurring = ?,
                               recurrence_type = ?, recurrence_interval = ?, recurrence_count = ?, recurrence_end_date = ?
                        WHERE id = ?
                    ");

                    $stmt->execute([
                        $_POST['title'],
                        $_POST['description'],
                        !empty($_POST['requirements']) ? $_POST['requirements'] : null,
                        $_POST['start_datetime'], // Use start_datetime as event_date
                        $_POST['location'],
                        !empty($_POST['capacity']) ? (int)$_POST['capacity'] : null,
                        !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null,
                        ($_POST['status'] === 'published') ? 1 : 0, // Convert status to is_active
                        $isRecurring ? 1 : 0,
                        $isRecurring ? $_POST['recurrence_type'] : null,
                        $isRecurring ? (int)$_POST['recurrence_interval'] : null,
                        $isRecurring ? (int)$_POST['recurrence_count'] : null,
                        $isRecurring && !empty($_POST['recurrence_end_date']) ? $_POST['recurrence_end_date'] : null,
                        $eventId
                    ]);

                    // Handle recurring status changes
                    if ($wasRecurring && !$isRecurring) {
                        // Event was recurring but now is not - clean up recurring instances if they exist
                        try {
                            $stmt = $conn->prepare("DELETE FROM recurring_event_instances WHERE parent_event_id = ?");
                            $stmt->execute([$eventId]);
                        } catch (PDOException $e) {
                            // Table might not exist, that's okay
                            error_log("Note: Could not clean up recurring instances (table may not exist): " . $e->getMessage());
                        }
                    } elseif (!$wasRecurring && $isRecurring) {
                        // Event was not recurring but now is - create new recurring instances
                        if (class_exists('RecurringEventManager')) {
                            $recurringResult = $recurringEventManager->createRecurringInstances($eventId, [
                                'type' => $_POST['recurrence_type'],
                                'interval' => (int)$_POST['recurrence_interval'],
                                'count' => (int)$_POST['recurrence_count'],
                                'end_date' => !empty($_POST['recurrence_end_date']) ? $_POST['recurrence_end_date'] : null
                            ]);

                            if (!$recurringResult['success']) {
                                error_log("Failed to create recurring instances: " . $recurringResult['message']);
                            }
                        }
                    } elseif ($wasRecurring && $isRecurring) {
                        // Event was recurring and still is - update existing instances if manager exists
                        if (class_exists('RecurringEventManager')) {
                            // Delete old instances and create new ones with updated pattern
                            try {
                                $stmt = $conn->prepare("DELETE FROM recurring_event_instances WHERE parent_event_id = ?");
                                $stmt->execute([$eventId]);

                                $recurringResult = $recurringEventManager->createRecurringInstances($eventId, [
                                    'type' => $_POST['recurrence_type'],
                                    'interval' => (int)$_POST['recurrence_interval'],
                                    'count' => (int)$_POST['recurrence_count'],
                                    'end_date' => !empty($_POST['recurrence_end_date']) ? $_POST['recurrence_end_date'] : null
                                ]);

                                if (!$recurringResult['success']) {
                                    error_log("Failed to update recurring instances: " . $recurringResult['message']);
                                }
                            } catch (PDOException $e) {
                                error_log("Error updating recurring instances: " . $e->getMessage());
                            }
                        }
                    }

                    // Handle file uploads for Additional Documents (PDF)
                    $file_upload_result = handleEventFileUploads((int)$_POST['event_id'], $_SESSION['admin_id']);

                    $message = __('event_updated_successfully');

                    // Add recurring status change messages
                    if ($wasRecurring && !$isRecurring) {
                        $message .= " Event is no longer recurring.";
                    } elseif (!$wasRecurring && $isRecurring) {
                        $message .= " Event is now set to recurring.";
                    } elseif ($wasRecurring && $isRecurring) {
                        $message .= " Recurring pattern updated.";
                    }

                    if (!empty($file_upload_result['errors'])) {
                        $message .= " Note: Some file uploads failed: " . implode(', ', $file_upload_result['errors']);
                    } else if (!empty($file_upload_result['uploaded_files'])) {
                        $message .= " " . count($file_upload_result['uploaded_files']) . " file(s) uploaded successfully.";
                    }
                } catch (PDOException $e) {
                    $error = __('error_updating_event') . ": " . $e->getMessage();
                }
                break;
                
            case 'delete_event':
                try {
                    $stmt = $conn->prepare("DELETE FROM events WHERE id = ?");
                    $stmt->execute([(int)$_POST['event_id']]);
                    $message = __('event_deleted_successfully');
                } catch (PDOException $e) {
                    $error = __('error_deleting_event') . ": " . $e->getMessage();
                }
                break;
        }
    }
}

// Include pagination component
require_once 'includes/pagination.php';

// Get events with pagination
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$limit = isset($_GET['limit']) ? max(10, min(100, intval($_GET['limit']))) : 20;

// Build search and filter conditions
$where_conditions = [];
$params = [];

if (!empty($_GET['search'])) {
    $where_conditions[] = "(e.title LIKE ? OR e.description LIKE ? OR e.location LIKE ?)";
    $search_term = '%' . $_GET['search'] . '%';
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

if (!empty($_GET['status'])) {
    if ($_GET['status'] === 'published') {
        $where_conditions[] = "e.is_active = 1 AND e.event_date >= NOW()";
    } elseif ($_GET['status'] === 'draft') {
        $where_conditions[] = "e.is_active = 0";
    } elseif ($_GET['status'] === 'completed') {
        $where_conditions[] = "e.event_date < NOW()";
    } elseif ($_GET['status'] === 'cancelled') {
        $where_conditions[] = "e.is_active = 0"; // Treat cancelled as inactive for now
    }
}

if (!empty($_GET['category'])) {
    $where_conditions[] = "e.category_id = ?";
    $params[] = (int)$_GET['category'];
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get total count for pagination
$count_sql = "SELECT COUNT(*) FROM events e $where_clause";
$count_stmt = $conn->prepare($count_sql);
$count_stmt->execute($params);
$total_events = $count_stmt->fetchColumn();

// Calculate pagination
$pagination = calculate_pagination($total_events, $page, $limit);
$offset = $pagination['offset'];

// Get events with category names and RSVP counts (including guests)
$sql = "
    SELECT e.*,
           ec.name as category_name,
           (
               COALESCE(member_rsvp_counts.attending_count, 0) +
               COALESCE(guest_rsvp_counts.attending_count, 0)
           ) as attending_count,
           0 as file_count
    FROM events e
    LEFT JOIN event_categories ec ON e.category_id = ec.id
    LEFT JOIN (
        SELECT event_id,
               COUNT(CASE WHEN status = 'attending' THEN 1 END) as attending_count
        FROM event_rsvps
        GROUP BY event_id
    ) member_rsvp_counts ON e.id = member_rsvp_counts.event_id
    LEFT JOIN (
        SELECT event_id,
               COUNT(CASE WHEN status = 'attending' THEN 1 END) as attending_count
        FROM event_rsvps_guests
        GROUP BY event_id
    ) guest_rsvp_counts ON e.id = guest_rsvp_counts.event_id
    $where_clause
    ORDER BY e.event_date DESC
    LIMIT $limit OFFSET $offset
";

$stmt = $conn->prepare($sql);
$stmt->execute($params);
$events = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get banner information for each event separately
if (!empty($events)) {
    $eventIds = array_column($events, 'id');
    $placeholders = str_repeat('?,', count($eventIds) - 1) . '?';

    $bannerStmt = $conn->prepare("
        SELECT DISTINCT event_id, file_path, file_type, file_name
        FROM event_files
        WHERE event_id IN ($placeholders) AND is_header_banner = 1
        GROUP BY event_id
    ");
    $bannerStmt->execute($eventIds);
    $banners = $bannerStmt->fetchAll(PDO::FETCH_ASSOC);

    // Create a lookup array for banners
    $bannerLookup = [];
    foreach ($banners as $banner) {
        $bannerLookup[$banner['event_id']] = $banner;
    }

    // Add banner information to events
    foreach ($events as &$event) {
        if (isset($bannerLookup[$event['id']])) {
            $banner = $bannerLookup[$event['id']];
            $event['header_banner_path'] = $banner['file_path'];
            $event['header_banner_type'] = $banner['file_type'];
            $event['header_banner_name'] = $banner['file_name'];
        } else {
            $event['header_banner_path'] = null;
            $event['header_banner_type'] = null;
            $event['header_banner_name'] = null;
        }
    }
    unset($event); // Important: Break the reference to avoid issues with subsequent loops
}

// Load event categories
try {
    $stmt = $conn->prepare("SELECT id, name FROM event_categories ORDER BY name");
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $categories = [];
    error_log("Error loading categories: " . $e->getMessage());
}

// Page title and header info
$page_title = __('events_management');
$page_header = __('events_management');
$page_description = __('events_management_description');

// Include header
include 'includes/header.php';
?>

<style>
.file-upload-area {
    transition: all 0.3s ease;
}

.file-upload-area:hover {
    border-color: #007bff !important;
    background-color: #f8f9fa !important;
}

.file-preview-card {
    transition: all 0.2s ease;
    border: 1px solid #dee2e6;
}

.file-preview-card:hover {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.min-w-0 {
    min-width: 0;
}

#uploadProgress {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
}
</style>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#eventModal">
            <i class="bi bi-plus-circle"></i> <?php _e('create_event'); ?>
        </button>
        <a href="event_categories.php" class="btn btn-outline-secondary ms-2">
            <i class="bi bi-tags"></i> <?php _e('manage_categories'); ?>
        </a>
        <a href="event_attendance.php" class="btn btn-outline-success ms-2">
            <i class="bi bi-calendar-check"></i> <?php _e('attendance_tracking'); ?>
        </a>
        <a href="event_reports.php" class="btn btn-outline-info ms-2">
            <i class="bi bi-graph-up"></i> <?php _e('reports'); ?>
        </a>
    </div>
    <div>
        <a href="<?php echo url_for('events.php'); ?>" class="btn btn-outline-primary">
            <i class="bi bi-calendar-event"></i> <?php _e('view_public_calendar'); ?>
        </a>
    </div>
</div>

<!-- Search and Filter Section -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label"><?php _e('search_events'); ?></label>
                <input type="text" class="form-control" id="search" name="search"
                       value="<?= htmlspecialchars($_GET['search'] ?? '') ?>"
                       placeholder="<?php _e('search_events_placeholder'); ?>">
            </div>
            <div class="col-md-3">
                <label for="category" class="form-label"><?php _e('category'); ?></label>
                <select class="form-select" id="category" name="category">
                    <option value=""><?php _e('all_categories'); ?></option>
                    <?php foreach ($categories as $category): ?>
                        <option value="<?= $category['id'] ?>"
                                <?= ($_GET['category'] ?? '') == $category['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($category['name']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label"><?php _e('status'); ?></label>
                <select class="form-select" id="status" name="status">
                    <option value=""><?php _e('all_statuses'); ?></option>
                    <option value="draft" <?= ($_GET['status'] ?? '') == 'draft' ? 'selected' : '' ?>><?php _e('draft'); ?></option>
                    <option value="published" <?= ($_GET['status'] ?? '') == 'published' ? 'selected' : '' ?>><?php _e('published'); ?></option>
                    <option value="cancelled" <?= ($_GET['status'] ?? '') == 'cancelled' ? 'selected' : '' ?>><?php _e('cancelled'); ?></option>
                    <option value="completed" <?= ($_GET['status'] ?? '') == 'completed' ? 'selected' : '' ?>><?php _e('completed'); ?></option>
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary w-100">
                    <i class="bi bi-search"></i> <?php _e('filter'); ?>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Events Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><?php _e('events'); ?> (<?= $total_events ?> <?php _e('total'); ?>)</h5>
    </div>
    <div class="card-body p-0">
        <?php if (empty($events)): ?>
            <div class="text-center py-5">
                <i class="bi bi-calendar-x display-1 text-muted"></i>
                <h4 class="mt-3"><?php _e('no_events_found'); ?></h4>
                <p class="text-muted"><?php _e('create_first_event'); ?></p>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#eventModal">
                    <i class="bi bi-plus-circle"></i> <?php _e('create_event'); ?>
                </button>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th><?php _e('event'); ?></th>
                            <th><?php _e('date_time'); ?></th>
                            <th><?php _e('location'); ?></th>
                            <th><?php _e('category'); ?></th>
                            <th><?php _e('status'); ?></th>
                            <th><?php _e('rsvps'); ?></th>
                            <th><?php _e('actions'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($events as $event): ?>
                            <?php
                            // Determine event status for CSS classes
                            $event_datetime = strtotime($event['event_date']);
                            $is_past = $event_datetime < time();
                            $is_today = date('Y-m-d', $event_datetime) === date('Y-m-d');
                            $is_upcoming = $event_datetime > time() && $event_datetime < strtotime('+7 days');

                            $row_class = 'event-row';
                            if ($is_past) {
                                $row_class .= ' past-event';
                            } elseif ($is_today) {
                                $row_class .= ' today-event';
                            } elseif ($is_upcoming) {
                                $row_class .= ' upcoming-event';
                            }
                            ?>
                            <tr class="<?= $row_class ?>">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <!-- Header Banner Thumbnail -->
                                        <?php if (!empty($event['header_banner_path'])): ?>
                                            <div class="me-3">
                                                <?php if (!empty($event['header_banner_type']) && strpos($event['header_banner_type'], 'image/') === 0): ?>
                                                    <?php
                                                    // Build the correct path for admin pages using helper function
                                                    $dbBannerPath = $event['header_banner_path'];
                                                    $banner_path = admin_file_path($dbBannerPath);

                                                    // Generate thumbnail path
                                                    $thumbnail_path = str_replace('/promotional/', '/thumbnails/', dirname($banner_path)) . '/thumb_' . basename($banner_path);

                                                    // Check if files exist using correct server path
                                                    $serverThumbnailPath = __DIR__ . '/' . $thumbnail_path;
                                                    $serverBannerPath = __DIR__ . '/' . $banner_path;

                                                    $display_path = (file_exists($serverThumbnailPath)) ? $thumbnail_path :
                                                                   (file_exists($serverBannerPath) ? $banner_path : $banner_path);
                                                    ?>
                                                    <img src="<?= htmlspecialchars($display_path) ?>"
                                                         alt="<?= htmlspecialchars($event['title']) ?>"
                                                         class="rounded"
                                                         style="width: 60px; height: 40px; object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="d-flex align-items-center justify-content-center bg-light rounded"
                                                         style="width: 60px; height: 40px;">
                                                        <i class="bi bi-file-pdf text-danger"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        <?php else: ?>
                                            <div class="me-3">
                                                <div class="d-flex align-items-center justify-content-center bg-light rounded"
                                                     style="width: 60px; height: 40px;">
                                                    <i class="bi bi-calendar-event text-muted"></i>
                                                </div>
                                            </div>
                                        <?php endif; ?>

                                        <!-- Event Details -->
                                        <div>
                                            <strong><?= htmlspecialchars($event['title']) ?></strong>
                                            <?php if ($event['is_recurring']): ?>
                                                <span class="badge bg-info ms-2" title="Recurring Event">
                                                    <i class="bi bi-arrow-repeat"></i> Recurring
                                                </span>
                                            <?php endif; ?>
                                            <?php if (!empty($event['header_banner_path'])): ?>
                                                <span class="badge bg-success ms-2">
                                                    <i class="bi bi-image"></i> Banner
                                                </span>
                                            <?php endif; ?>
                                            <?php if ($event['description']): ?>
                                                <br><small class="text-muted">
                                                    <?= htmlspecialchars(substr($event['description'], 0, 80)) ?>
                                                    <?= strlen($event['description']) > 80 ? '...' : '' ?>
                                                </small>
                                            <?php endif; ?>
                                            <?php if ($event['is_recurring']): ?>
                                                <br><small class="text-info">
                                                    <i class="bi bi-arrow-repeat"></i>
                                                    Repeats <?= ucfirst($event['recurrence_type']) ?>
                                                    <?php if ($event['recurrence_interval'] > 1): ?>
                                                        (every <?= $event['recurrence_interval'] ?> <?= $event['recurrence_type'] === 'daily' ? 'days' : ($event['recurrence_type'] === 'weekly' ? 'weeks' : ($event['recurrence_type'] === 'monthly' ? 'months' : 'years')) ?>)
                                                    <?php endif; ?>
                                                    <?php
                                                    // Calculate next occurrence using the new dynamic system
                                                    $nextOccurrence = $recurringEventManager->getNextOccurrence($event['id']);
                                                    if ($nextOccurrence) {
                                                        $nextDate = new DateTime($nextOccurrence['date']);
                                                        $now = new DateTime();
                                                        if ($nextDate > $now) {
                                                            echo '<br><span class="badge bg-success"><i class="bi bi-calendar-plus"></i> Next: ' . $nextDate->format('M j, Y g:i A') . '</span>';
                                                        }
                                                    }
                                                    ?>
                                                    <span class="badge bg-light text-dark ms-1" title="Single event entry - occurrences calculated dynamically">
                                                        <i class="bi bi-check-circle"></i> Clean Display
                                                    </span>
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <?php
                                        $event_datetime = strtotime($event['event_date']);
                                        $is_past = $event_datetime < time();
                                        $is_today = date('Y-m-d', $event_datetime) === date('Y-m-d');
                                        $is_upcoming = $event_datetime > time() && $event_datetime < strtotime('+7 days');
                                        ?>

                                        <div class="d-flex align-items-center">
                                            <strong class="<?= $is_past ? 'text-muted' : ($is_today ? 'text-warning' : ($is_upcoming ? 'text-primary' : '')) ?>">
                                                <?= date('M j, Y', $event_datetime) ?>
                                            </strong>
                                            <?php if ($is_past): ?>
                                                <span class="badge bg-secondary ms-2" title="Event has passed">
                                                    <i class="bi bi-clock-history"></i> Past
                                                </span>
                                            <?php elseif ($is_today): ?>
                                                <span class="badge bg-warning text-dark ms-2" title="Event is today">
                                                    <i class="bi bi-calendar-day"></i> Today
                                                </span>
                                            <?php elseif ($is_upcoming): ?>
                                                <span class="badge bg-primary ms-2" title="Event is coming up soon">
                                                    <i class="bi bi-calendar-week"></i> Soon
                                                </span>
                                            <?php endif; ?>
                                        </div>

                                        <small class="<?= $is_past ? 'text-muted' : 'text-muted' ?>">
                                            <?= date('g:i A', $event_datetime) ?>
                                            <?php if ($is_past): ?>
                                                <span class="text-muted"> (<?= time_elapsed_string($event_datetime) ?> ago)</span>
                                            <?php endif; ?>
                                        </small>

                                        <?php if ($event['file_count'] > 0): ?>
                                            <br><small class="text-info">
                                                <i class="bi bi-file-pdf"></i> <?= $event['file_count'] ?> <?php _e('files'); ?>
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td><?= htmlspecialchars($event['location'] ?? __('tbd')) ?></td>
                                <td>
                                    <?php if ($event['category_name']): ?>
                                        <span class="badge bg-secondary"><?= htmlspecialchars($event['category_name']) ?></span>
                                    <?php else: ?>
                                        <span class="text-muted"><?php _e('uncategorized'); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    // Use new status field with fallback to old logic
                                    $event_status = $event['status'] ?? 'draft';
                                    $event_datetime = strtotime($event['event_date']);
                                    $is_past = $event_datetime < time();

                                    // Auto-update status for past events if still published
                                    if ($is_past && $event_status === 'published') {
                                        $event_status = 'completed';
                                    }

                                    $status_config = [
                                        'draft' => ['class' => 'bg-warning text-dark', 'icon' => 'bi-eye-slash', 'text' => 'Draft', 'desc' => 'Hidden from users'],
                                        'published' => ['class' => 'bg-success', 'icon' => 'bi-eye', 'text' => 'Published', 'desc' => 'Visible to users'],
                                        'cancelled' => ['class' => 'bg-danger', 'icon' => 'bi-x-circle', 'text' => 'Cancelled', 'desc' => 'Event cancelled'],
                                        'completed' => ['class' => 'bg-secondary', 'icon' => 'bi-clock-history', 'text' => 'Completed', 'desc' => 'Event has ended'],
                                        'archived' => ['class' => 'bg-dark', 'icon' => 'bi-archive', 'text' => 'Archived', 'desc' => 'Archived event']
                                    ];

                                    $config = $status_config[$event_status] ?? $status_config['draft'];
                                    ?>

                                    <span class="badge <?= $config['class'] ?>">
                                        <i class="<?= $config['icon'] ?>"></i> <?= $config['text'] ?>
                                    </span>
                                    <br><small class="text-muted"><?= $config['desc'] ?></small>

                                    <?php if ($event['allow_late_registration'] ?? true): ?>
                                        <br><small class="text-info">
                                            <i class="bi bi-clock"></i> Late reg:
                                            <?php if (($event['late_registration_cutoff_hours'] ?? 0) > 0): ?>
                                                <?= $event['late_registration_cutoff_hours'] ?>h before
                                            <?php else: ?>
                                                Until start
                                            <?php endif; ?>
                                        </small>
                                    <?php else: ?>
                                        <br><small class="text-warning">
                                            <i class="bi bi-clock-slash"></i> No late registration
                                        </small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-primary">
                                        <?= $event['attending_count'] ?>
                                        <?php if ($event['max_attendees']): ?>
                                            / <?= $event['max_attendees'] ?>
                                        <?php endif; ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary edit-event-btn"
                                                data-event='<?= htmlspecialchars(json_encode($event), ENT_QUOTES, 'UTF-8') ?>'>
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary event-status-btn"
                                                data-event-id="<?= $event['id'] ?>"
                                                data-event-title="<?= htmlspecialchars($event['title']) ?>"
                                                data-event-status="<?= $event['status'] ?? 'draft' ?>"
                                                data-allow-late-reg="<?= $event['allow_late_registration'] ?? 1 ?>"
                                                data-cutoff-hours="<?= $event['late_registration_cutoff_hours'] ?? 0 ?>"
                                                title="Event Status & Settings">
                                            <i class="bi bi-gear"></i>
                                        </button>
                                        <?php if ($event['is_recurring'] ?? false): ?>
                                            <button type="button" class="btn btn-outline-info recurring-manage-btn"
                                                    data-event-id="<?= $event['id'] ?>"
                                                    data-event-title="<?= htmlspecialchars($event['title']) ?>"
                                                    title="Manage Recurring Series">
                                                <i class="bi bi-arrow-repeat"></i>
                                            </button>
                                        <?php else: ?>
                                            <button type="button" class="btn btn-outline-success recurring-create-btn"
                                                    data-event-id="<?= $event['id'] ?>"
                                                    data-event-title="<?= htmlspecialchars($event['title']) ?>"
                                                    title="Make Recurring">
                                                <i class="bi bi-plus-circle"></i>
                                            </button>
                                        <?php endif; ?>
                                        <a href="<?php echo admin_url_for('event_sessions.php?event_id=' . $event['id']); ?>" class="btn btn-outline-warning" title="Manage Sessions">
                                            <i class="bi bi-collection"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-info view-event-btn"
                                                data-event='<?= htmlspecialchars(json_encode($event), ENT_QUOTES, 'UTF-8') ?>'>
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger delete-event-btn"
                                                data-id="<?= $event['id'] ?>" data-title="<?= htmlspecialchars($event['title']) ?>">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>

    <!-- Enhanced Pagination -->
    <?php if ($pagination['total_pages'] > 1): ?>
        <div class="card-footer">
            <?php
            // Set global variable for JavaScript
            $GLOBALS['total_pages'] = $pagination['total_pages'];

            // Prepare URL parameters to preserve
            $url_params = [];
            if (!empty($_GET['search'])) {
                $url_params['search'] = $_GET['search'];
            }
            if (!empty($_GET['category'])) {
                $url_params['category'] = $_GET['category'];
            }
            if (!empty($_GET['status'])) {
                $url_params['status'] = $_GET['status'];
            }

            echo generate_pagination(
                $pagination['current_page'],
                $pagination['total_pages'],
                $pagination['total_records'],
                $pagination['records_per_page'],
                'events.php',
                $url_params,
                'page'
            );
            ?>
        </div>
    <?php endif; ?>
</div>

<!-- Event Modal -->
<div class="modal fade" id="eventModal" tabindex="-1" aria-labelledby="eventModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="eventModalLabel"><?php _e('create_event'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="<?php _e('close'); ?>"></button>
            </div>
            <form id="eventForm" method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="action" id="eventAction" value="create_event">
                    <input type="hidden" name="event_id" id="eventId">

                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="eventTitle" class="form-label"><?php _e('event_title'); ?> *</label>
                            <input type="text" class="form-control" id="eventTitle" name="title" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="eventCategory" class="form-label"><?php _e('category'); ?></label>
                            <select class="form-select" id="eventCategory" name="category_id">
                                <option value=""><?php _e('select_category'); ?></option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= $category['id'] ?>"><?= htmlspecialchars($category['name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="eventDescription" class="form-label"><?php _e('description'); ?></label>
                        <textarea class="form-control" id="eventDescription" name="description" rows="4"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="eventStartDate" class="form-label"><?php _e('start_date_time'); ?> *</label>
                            <input type="datetime-local" class="form-control" id="eventStartDate" name="start_datetime" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="eventEndDate" class="form-label"><?php _e('end_date_time'); ?> *</label>
                            <input type="datetime-local" class="form-control" id="eventEndDate" name="end_datetime" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="eventLocation" class="form-label"><?php _e('location'); ?></label>
                            <input type="text" class="form-control" id="eventLocation" name="location" placeholder="<?php _e('event_location_placeholder'); ?>" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="eventCapacity" class="form-label"><?php _e('capacity'); ?></label>
                            <input type="number" class="form-control" id="eventCapacity" name="capacity" min="1" placeholder="<?php _e('optional'); ?>">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="eventRequirements" class="form-label"><?php _e('requirements_notes'); ?></label>
                        <textarea class="form-control" id="eventRequirements" name="requirements" rows="2" placeholder="<?php _e('requirements_placeholder'); ?>"></textarea>
                    </div>

                    <!-- Promotional Materials Section -->
                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">
                            <i class="bi bi-image"></i> Promotional Materials
                        </h6>

                        <!-- File Upload Area -->
                        <div class="mb-3">
                            <label for="promotionalFiles" class="form-label">Upload Promotional Materials</label>
                            <div id="promotionalUploadArea" class="file-upload-area border-2 border-dashed rounded p-4 text-center"
                                 style="border-color: #dee2e6; cursor: pointer; user-select: none; position: relative; z-index: 1;"
                                 onclick="document.getElementById('promotionalFiles').click()"
                                 onmouseover="this.style.backgroundColor='#f8f9fa'"
                                 onmouseout="this.style.backgroundColor='transparent'">
                                <i class="bi bi-cloud-upload fs-1 text-primary mb-2"></i>
                                <p class="mb-1">Click to upload or drag and drop</p>
                                <small class="text-muted">Images (JPG, PNG, GIF) and PDFs • Max 15MB each</small>
                            </div>

                            <!-- Alternative upload button -->
                            <div class="mt-2 text-center">
                                <button type="button" class="btn btn-primary btn-sm" onclick="document.getElementById('promotionalFiles').click()">
                                    <i class="bi bi-folder2-open"></i> Browse Files
                                </button>
                            </div>

                            <!-- File preview area -->
                            <div id="filePreviewArea" class="mt-3" style="display: none;">
                                <h6 class="fw-bold mb-2">Selected Files:</h6>
                                <div id="filePreviewList" class="row g-2"></div>
                            </div>

                            <!-- Upload progress -->
                            <div id="uploadProgress" class="mt-3" style="display: none;">
                                <div class="d-flex align-items-center">
                                    <div class="spinner-border spinner-border-sm me-2" role="status">
                                        <span class="visually-hidden">Uploading...</span>
                                    </div>
                                    <span>Processing files...</span>
                                </div>
                            </div>

                            <input type="file" class="form-control d-none" id="promotionalFiles"
                                   name="promotional_files[]" multiple
                                   accept=".jpg,.jpeg,.png,.gif,.pdf"
                                   onchange="handleFileSelection(this.files)"
                                   onclick="console.log('File input clicked')">
                        </div>

                        <!-- Header Banner Selection -->
                        <div class="mb-3">
                            <label class="form-label">Header Banner</label>
                            <div id="headerBannerSelection" class="border rounded p-3">
                                <p class="text-muted mb-0">
                                    <i class="bi bi-info-circle"></i>
                                    Upload promotional materials above, then select one as the header banner
                                </p>
                            </div>
                        </div>

                        <!-- Existing Files Display -->
                        <div id="existingPromotionalMaterials" class="mt-3"></div>

                        <!-- Legacy PDF Files -->
                        <div class="mt-3">
                            <label for="eventFiles" class="form-label">Additional Documents (PDF)</label>
                            <input type="file" class="form-control" id="eventFiles" name="event_files[]" multiple accept=".pdf">
                            <small class="form-text text-muted">Upload PDF documents for additional event information</small>
                            <div id="existingFiles" class="mt-2"></div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="eventStatus" class="form-label"><?php _e('status'); ?> <small class="text-muted">(Only published events are visible to users)</small></label>
                            <select class="form-select" id="eventStatus" name="status">
                                <option value="draft"><?php _e('draft'); ?> - Hidden from users</option>
                                <option value="published" selected><?php _e('published'); ?> - Visible to users</option>
                                <option value="cancelled"><?php _e('cancelled'); ?> - Hidden from users</option>
                                <option value="completed"><?php _e('completed'); ?> - Hidden from users</option>
                            </select>
                        </div>
                        <div class="col-md-8 mb-3">
                            <label for="eventRegDeadline" class="form-label"><?php _e('registration_deadline'); ?></label>
                            <input type="datetime-local" class="form-control" id="eventRegDeadline" name="registration_deadline">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="eventPublic" name="is_public" checked>
                                <label class="form-check-label" for="eventPublic">
                                    <?php _e('public_event'); ?>
                                </label>
                                <small class="form-text text-muted d-block"><?php _e('visible_public_calendar'); ?></small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="eventRegistration" name="registration_required">
                                <label class="form-check-label" for="eventRegistration">
                                    <?php _e('registration_required'); ?>
                                </label>
                                <small class="form-text text-muted d-block"><?php _e('require_rsvp_attend'); ?></small>
                            </div>
                        </div>
                    </div>

                    <!-- Recurring Events Section -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="isRecurring" name="is_recurring">
                                <label class="form-check-label" for="isRecurring" title="Toggle to make this event recurring or stop an existing recurring pattern">
                                    <i class="bi bi-arrow-repeat"></i> Make this a recurring event
                                </label>
                                <small class="form-text text-muted d-block">
                                    Check to create a recurring series, or uncheck to stop an existing recurring pattern
                                </small>
                            </div>
                        </div>
                        <div class="card-body" id="recurringOptions" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="recurrenceType" class="form-label">Recurrence Pattern</label>
                                    <select class="form-select" id="recurrenceType" name="recurrence_type">
                                        <option value="daily">Daily</option>
                                        <option value="weekly" selected>Weekly</option>
                                        <option value="monthly">Monthly</option>
                                        <option value="yearly">Yearly</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="recurrenceInterval" class="form-label">Every</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="recurrenceInterval" name="recurrence_interval" min="1" max="12" value="1">
                                        <span class="input-group-text" id="intervalUnit">week(s)</span>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <label for="recurrenceCount" class="form-label">Number of Occurrences</label>
                                    <input type="number" class="form-control" id="recurrenceCount" name="recurrence_count" min="1" max="52" value="10">
                                    <small class="text-muted">Maximum 52 occurrences</small>
                                </div>
                                <div class="col-md-6">
                                    <label for="recurrenceEndDate" class="form-label">End Date (Optional)</label>
                                    <input type="date" class="form-control" id="recurrenceEndDate" name="recurrence_end_date">
                                    <small class="text-muted">Leave blank to use occurrence count</small>
                                </div>
                            </div>

                            <div class="alert alert-info mt-3">
                                <i class="bi bi-info-circle"></i>
                                <strong>Note:</strong> This will create a recurring event series. You can manage individual occurrences later.
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php _e('cancel'); ?></button>
                    <button type="submit" class="btn btn-primary" id="submitButton">
                        <span id="submitButtonText"><?php _e('create_event'); ?></span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-confirmation">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel"><?php _e('confirm_delete'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="<?php _e('close'); ?>"></button>
            </div>
            <div class="modal-body">
                <p><?php _e('delete_event_confirmation'); ?> "<span id="deleteEventTitle"></span>"?</p>
                <p class="text-danger"><strong><?php _e('action_cannot_undone'); ?></strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php _e('cancel'); ?></button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="delete_event">
                    <input type="hidden" name="event_id" id="deleteEventId">
                    <button type="submit" class="btn btn-danger"><?php _e('delete_event'); ?></button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- View Event Modal -->
<div class="modal fade" id="viewEventModal" tabindex="-1" aria-labelledby="viewEventModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewEventModalLabel"><?php _e('event_details'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="<?php _e('close'); ?>"></button>
            </div>
            <div class="modal-body">
                <!-- Header Banner Section -->
                <div class="row mb-3" id="viewEventBannerRow" style="display: none;">
                    <div class="col-12">
                        <div class="text-center">
                            <img id="viewEventBanner" src="" alt="" class="img-fluid rounded" style="max-height: 200px;">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-8">
                        <h4 id="viewEventTitle"></h4>
                        <p class="text-muted mb-3" id="viewEventDescription"></p>
                    </div>
                    <div class="col-md-4 text-end">
                        <span class="badge fs-6" id="viewEventStatus"></span>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <h6><i class="bi bi-calendar"></i> <?php _e('date_time'); ?></h6>
                        <p id="viewEventDateTime"></p>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="bi bi-geo-alt"></i> <?php _e('location'); ?></h6>
                        <p id="viewEventLocation"></p>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <h6><i class="bi bi-people"></i> <?php _e('capacity'); ?></h6>
                        <p id="viewEventCapacity"></p>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="bi bi-tag"></i> <?php _e('category'); ?></h6>
                        <p id="viewEventCategory"></p>
                    </div>
                </div>

                <div class="row mb-3" id="viewEventRequirementsRow" style="display: none;">
                    <div class="col-12">
                        <h6><i class="bi bi-info-circle"></i> <?php _e('requirements_notes'); ?></h6>
                        <p id="viewEventRequirements"></p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="bi bi-calendar-check"></i> <?php _e('registration'); ?></h6>
                        <p id="viewEventRegistration"></p>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="bi bi-eye"></i> <?php _e('visibility'); ?></h6>
                        <p id="viewEventVisibility"></p>
                    </div>
                </div>

                <div class="row" id="viewEventFilesRow" style="display: none;">
                    <div class="col-12">
                        <h6><i class="bi bi-file-pdf"></i> <?php _e('event_files'); ?></h6>
                        <div id="viewEventFiles"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php _e('close'); ?></button>
                <button type="button" class="btn btn-primary" id="editFromViewBtn"><?php _e('edit_event'); ?></button>
            </div>
        </div>
    </div>
</div>

<!-- Event Status Management Modal -->
<div class="modal fade" id="eventStatusModal" tabindex="-1" aria-labelledby="eventStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="eventStatusModalLabel">Event Status & Settings</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="eventStatusForm">
                    <input type="hidden" id="statusEventId" name="event_id">

                    <!-- Event Status Section -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-toggle-on"></i> Event Status Management</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Current Status:</strong> <span id="currentEventStatus" class="badge"></span></p>
                                    <p><strong>Event:</strong> <span id="statusEventTitle"></span></p>
                                </div>
                                <div class="col-md-6">
                                    <div class="btn-group-vertical w-100" role="group">
                                        <button type="button" class="btn btn-outline-success btn-sm status-action-btn" data-action="activate">
                                            <i class="bi bi-play-circle"></i> Activate Event
                                        </button>
                                        <button type="button" class="btn btn-outline-warning btn-sm status-action-btn" data-action="deactivate">
                                            <i class="bi bi-pause-circle"></i> Deactivate Event
                                        </button>
                                        <button type="button" class="btn btn-outline-danger btn-sm status-action-btn" data-action="cancel">
                                            <i class="bi bi-x-circle"></i> Cancel Event
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm status-action-btn" data-action="archive">
                                            <i class="bi bi-archive"></i> Archive Event
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div id="statusReasonSection" class="mt-3" style="display: none;">
                                <label for="statusReason" class="form-label">Reason (Optional)</label>
                                <textarea class="form-control" id="statusReason" name="reason" rows="2" placeholder="Enter reason for status change..."></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Late Registration Controls -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-clock"></i> Late Registration Controls</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="allowLateRegistration" name="allow_late_registration">
                                        <label class="form-check-label" for="allowLateRegistration">
                                            Allow Late Registration
                                        </label>
                                    </div>
                                    <small class="text-muted">Allow users to register after the event has started</small>
                                </div>
                                <div class="col-md-6">
                                    <label for="lateRegCutoffHours" class="form-label">Registration Cutoff (Hours Before Event)</label>
                                    <input type="number" class="form-control" id="lateRegCutoffHours" name="late_registration_cutoff_hours" min="0" max="168" value="0">
                                    <small class="text-muted">0 = Allow registration until event starts</small>
                                </div>
                            </div>

                            <div class="mt-3">
                                <button type="button" class="btn btn-primary" id="updateLateRegBtn">
                                    <i class="bi bi-save"></i> Update Late Registration Settings
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Recurring Events Modal -->
<div class="modal fade" id="recurringEventModal" tabindex="-1" aria-labelledby="recurringEventModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="recurringEventModalLabel">Recurring Event Configuration</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="recurringEventForm">
                    <input type="hidden" id="recurringEventId" name="event_id">

                    <div id="createRecurringSection">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            <strong>Create Recurring Series</strong><br>
                            This will create multiple instances of this event based on the recurrence pattern you specify.
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <label for="recurrenceType" class="form-label">Recurrence Pattern</label>
                                <select class="form-select" id="recurrenceType" name="recurrence_type" required>
                                    <option value="daily">Daily</option>
                                    <option value="weekly" selected>Weekly</option>
                                    <option value="monthly">Monthly</option>
                                    <option value="yearly">Yearly</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="recurrenceInterval" class="form-label">Every</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="recurrenceInterval" name="recurrence_interval" min="1" max="12" value="1" required>
                                    <span class="input-group-text" id="intervalUnit">week(s)</span>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label for="recurrenceCount" class="form-label">Number of Instances</label>
                                <input type="number" class="form-control" id="recurrenceCount" name="recurrence_count" min="1" max="52" value="10" required>
                                <small class="text-muted">Maximum 52 instances</small>
                            </div>
                            <div class="col-md-6">
                                <label for="recurrenceEndDate" class="form-label">End Date (Optional)</label>
                                <input type="date" class="form-control" id="recurrenceEndDate" name="recurrence_end_date">
                                <small class="text-muted">Leave blank to use instance count</small>
                            </div>
                        </div>

                        <div class="mt-4">
                            <button type="button" class="btn btn-success" id="createRecurringBtn">
                                <i class="bi bi-plus-circle"></i> Create Recurring Series
                            </button>
                        </div>
                    </div>

                    <div id="manageRecurringSection" style="display: none;">
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            <strong>Manage Recurring Series</strong><br>
                            This event is part of a recurring series. You can view instances or delete the entire series.
                        </div>

                        <div id="recurringInstancesList">
                            <!-- Instances will be loaded here -->
                        </div>

                        <div class="mt-3">
                            <button type="button" class="btn btn-info" id="viewInstancesBtn">
                                <i class="bi bi-list"></i> View All Instances
                            </button>
                            <button type="button" class="btn btn-danger" id="deleteSeriesBtn">
                                <i class="bi bi-trash"></i> Delete Entire Series
                            </button>
                        </div>

                        <div id="deleteSeriesOptions" class="mt-3" style="display: none;">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="deleteInstances" name="delete_instances" checked>
                                <label class="form-check-label" for="deleteInstances">
                                    Also delete all future instances
                                </label>
                            </div>
                            <div class="mt-2">
                                <button type="button" class="btn btn-danger" id="confirmDeleteSeriesBtn">
                                    <i class="bi bi-trash"></i> Confirm Delete Series
                                </button>
                                <button type="button" class="btn btn-secondary" id="cancelDeleteSeriesBtn">
                                    Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
    // Initialize file upload functionality
    function initializeFileUpload() {
        console.log('Initializing file upload functionality');

        const fileInput = document.getElementById('promotionalFiles');
        const uploadArea = document.getElementById('promotionalUploadArea');

        if (!fileInput) {
            console.error('File input element not found');
            return;
        }

        if (!uploadArea) {
            console.error('Upload area element not found');
            return;
        }

        console.log('File upload elements found and ready');

        // Test click functionality
        window.testFileUpload = function() {
            console.log('Testing file upload click');
            fileInput.click();
        };
    }

document.addEventListener('DOMContentLoaded', function() {
    // Initialize file upload functionality
    initializeFileUpload();

    // Event Status Management
    document.querySelectorAll('.event-status-btn').forEach(button => {
        button.addEventListener('click', function() {
            const eventId = this.dataset.eventId;
            const eventTitle = this.dataset.eventTitle;
            const eventStatus = this.dataset.eventStatus;
            const allowLateReg = this.dataset.allowLateReg === '1';
            const cutoffHours = this.dataset.cutoffHours;

            // Populate modal
            document.getElementById('statusEventId').value = eventId;
            document.getElementById('statusEventTitle').textContent = eventTitle;

            // Update status badge
            const statusBadge = document.getElementById('currentEventStatus');
            statusBadge.textContent = eventStatus.charAt(0).toUpperCase() + eventStatus.slice(1);
            statusBadge.className = 'badge bg-' + getStatusColor(eventStatus);

            // Set late registration settings
            document.getElementById('allowLateRegistration').checked = allowLateReg;
            document.getElementById('lateRegCutoffHours').value = cutoffHours;

            // Show modal
            new bootstrap.Modal(document.getElementById('eventStatusModal')).show();
        });
    });

    // Status action buttons
    document.querySelectorAll('.status-action-btn').forEach(button => {
        button.addEventListener('click', function() {
            const action = this.dataset.action;
            const reasonSection = document.getElementById('statusReasonSection');

            // Show reason section for cancel action
            if (action === 'cancel') {
                reasonSection.style.display = 'block';
                document.getElementById('statusReason').required = true;
            } else {
                reasonSection.style.display = 'none';
                document.getElementById('statusReason').required = false;
            }

            // Confirm action
            if (confirm(`Are you sure you want to ${action} this event?`)) {
                performStatusAction(action);
            }
        });
    });

    // Recurring Events functionality
    document.querySelectorAll('.recurring-create-btn').forEach(button => {
        button.addEventListener('click', function() {
            const eventId = this.dataset.eventId;
            const eventTitle = this.dataset.eventTitle;

            document.getElementById('recurringEventId').value = eventId;
            document.getElementById('recurringEventModalLabel').textContent = 'Create Recurring Series: ' + eventTitle;

            // Show create section, hide manage section
            document.getElementById('createRecurringSection').style.display = 'block';
            document.getElementById('manageRecurringSection').style.display = 'none';

            new bootstrap.Modal(document.getElementById('recurringEventModal')).show();
        });
    });

    document.querySelectorAll('.recurring-manage-btn').forEach(button => {
        button.addEventListener('click', function() {
            const eventId = this.dataset.eventId;
            const eventTitle = this.dataset.eventTitle;

            document.getElementById('recurringEventId').value = eventId;
            document.getElementById('recurringEventModalLabel').textContent = 'Manage Recurring Series: ' + eventTitle;

            // Show manage section, hide create section
            document.getElementById('createRecurringSection').style.display = 'none';
            document.getElementById('manageRecurringSection').style.display = 'block';

            // Load instances
            loadRecurringInstances(eventId);

            new bootstrap.Modal(document.getElementById('recurringEventModal')).show();
        });
    });

    // Handle recurring events toggle in main form
    document.getElementById('isRecurring').addEventListener('change', function() {
        const recurringOptions = document.getElementById('recurringOptions');
        recurringOptions.style.display = this.checked ? 'block' : 'none';
    });

    // Update interval unit text based on recurrence type in main form
    document.getElementById('recurrenceType').addEventListener('change', function() {
        const intervalUnit = document.getElementById('intervalUnit');
        const units = {
            'daily': 'day(s)',
            'weekly': 'week(s)',
            'monthly': 'month(s)',
            'yearly': 'year(s)'
        };
        intervalUnit.textContent = units[this.value] || 'period(s)';
    });

    // Create recurring series
    document.getElementById('createRecurringBtn').addEventListener('click', function() {
        const form = document.getElementById('recurringEventForm');
        const formData = new FormData(form);
        formData.append('action', 'create_recurring');
        formData.append('ajax', '1');

        if (confirm('This will create multiple event instances. Continue?')) {
            fetch('events.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while creating recurring series.');
            });
        }
    });

    // Delete series functionality
    document.getElementById('deleteSeriesBtn').addEventListener('click', function() {
        document.getElementById('deleteSeriesOptions').style.display = 'block';
    });

    document.getElementById('cancelDeleteSeriesBtn').addEventListener('click', function() {
        document.getElementById('deleteSeriesOptions').style.display = 'none';
    });

    document.getElementById('confirmDeleteSeriesBtn').addEventListener('click', function() {
        const eventId = document.getElementById('recurringEventId').value;
        const deleteInstances = document.getElementById('deleteInstances').checked;

        const formData = new FormData();
        formData.append('action', 'delete_recurring_series');
        formData.append('event_id', eventId);
        if (deleteInstances) {
            formData.append('delete_instances', '1');
        }
        formData.append('ajax', '1');

        if (confirm('Are you sure you want to delete this recurring series? This action cannot be undone.')) {
            fetch('events.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while deleting recurring series.');
            });
        }
    });

    // Update late registration settings
    document.getElementById('updateLateRegBtn').addEventListener('click', function() {
        const eventId = document.getElementById('statusEventId').value;
        const allowLateReg = document.getElementById('allowLateRegistration').checked;
        const cutoffHours = document.getElementById('lateRegCutoffHours').value;

        const formData = new FormData();
        formData.append('action', 'update_late_registration');
        formData.append('event_id', eventId);
        formData.append('late_registration_cutoff_hours', cutoffHours);
        if (allowLateReg) {
            formData.append('allow_late_registration', '1');
        }
        formData.append('ajax', '1');

        fetch('events.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Late registration settings updated successfully!');
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating settings.');
        });
    });

    // Helper function to get status color
    function getStatusColor(status) {
        const colors = {
            'draft': 'warning',
            'published': 'success',
            'cancelled': 'danger',
            'completed': 'secondary',
            'archived': 'dark'
        };
        return colors[status] || 'secondary';
    }

    // Perform status action
    function performStatusAction(action) {
        const eventId = document.getElementById('statusEventId').value;
        const reason = document.getElementById('statusReason').value;

        const formData = new FormData();
        formData.append('action', action);
        formData.append('event_id', eventId);
        formData.append('reason', reason);
        formData.append('ajax', '1');

        fetch('events.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating event status.');
        });
    }

    // Load recurring instances
    function loadRecurringInstances(eventId) {
        fetch('get_recurring_instances.php?event_id=' + eventId)
            .then(response => response.json())
            .then(data => {
                const container = document.getElementById('recurringInstancesList');
                if (data.success && data.instances.length > 0) {
                    let html = '<h6>Recurring Instances (' + data.instances.length + '):</h6>';
                    html += '<div class="list-group">';

                    data.instances.forEach(instance => {
                        const date = new Date(instance.event_date).toLocaleDateString();
                        const status = instance.status.charAt(0).toUpperCase() + instance.status.slice(1);
                        html += `
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${instance.title}</strong><br>
                                    <small class="text-muted">${date} - ${status}</small>
                                </div>
                                <span class="badge bg-secondary">#${instance.instance_number}</span>
                            </div>
                        `;
                    });

                    html += '</div>';
                    container.innerHTML = html;
                } else {
                    container.innerHTML = '<p class="text-muted">No instances found.</p>';
                }
            })
            .catch(error => {
                console.error('Error loading instances:', error);
                document.getElementById('recurringInstancesList').innerHTML = '<p class="text-danger">Error loading instances.</p>';
            });
    }

    // Show/hide upload message based on event ID
    const eventId = document.getElementById('eventId').value;
    const uploadMessage = document.getElementById('newEventUploadMessage');

    if (!eventId && uploadMessage) {
        uploadMessage.style.display = 'block';
    } else if (uploadMessage) {
        uploadMessage.style.display = 'none';
    }

    // Edit event functionality
    document.querySelectorAll('.edit-event-btn').forEach(button => {
        button.addEventListener('click', function() {
            const eventData = JSON.parse(this.getAttribute('data-event'));

            // Update modal title and form action
            document.getElementById('eventModalLabel').textContent = 'Edit Event';
            document.getElementById('eventAction').value = 'update_event';
            document.getElementById('eventId').value = eventData.id;
            document.getElementById('submitButtonText').textContent = 'Update Event';

            // Populate form fields
            document.getElementById('eventTitle').value = eventData.title || '';
            document.getElementById('eventDescription').value = eventData.description || '';

            // Handle dates properly - preserve existing dates if available
            if (eventData.event_date) {
                const startDate = eventData.event_date.replace(' ', 'T');
                document.getElementById('eventStartDate').value = startDate;

                // For end date, if we have a separate end_date field use it, otherwise add 1 hour to start
                if (eventData.end_date) {
                    document.getElementById('eventEndDate').value = eventData.end_date.replace(' ', 'T');
                } else {
                    // Add 1 hour to start date as default end time
                    const endDate = new Date(eventData.event_date);
                    endDate.setHours(endDate.getHours() + 1);
                    document.getElementById('eventEndDate').value = endDate.toISOString().slice(0, 16);
                }
            }

            document.getElementById('eventLocation').value = eventData.location || '';
            document.getElementById('eventCapacity').value = eventData.max_attendees || '';
            document.getElementById('eventCategory').value = eventData.category_id || '';

            // Fix status handling - check both is_active and status fields
            let statusValue = 'draft';
            if (eventData.status) {
                statusValue = eventData.status;
            } else if (eventData.is_active == '1') {
                statusValue = 'published';
            }
            document.getElementById('eventStatus').value = statusValue;
            document.getElementById('eventRequirements').value = eventData.requirements || '';
            document.getElementById('eventRegDeadline').value = '';
            document.getElementById('eventPublic').checked = true;
            document.getElementById('eventRegistration').checked = false;

            // Populate recurring fields
            const isRecurring = eventData.is_recurring == '1';
            document.getElementById('isRecurring').checked = isRecurring;
            document.getElementById('recurringOptions').style.display = isRecurring ? 'block' : 'none';

            if (isRecurring) {
                document.getElementById('recurrenceType').value = eventData.recurrence_type || 'weekly';
                document.getElementById('recurrenceInterval').value = eventData.recurrence_interval || 1;
                document.getElementById('recurrenceCount').value = eventData.recurrence_count || 10;
                document.getElementById('recurrenceEndDate').value = eventData.recurrence_end_date || '';

                // Update interval unit text
                const units = {
                    'daily': 'day(s)',
                    'weekly': 'week(s)',
                    'monthly': 'month(s)',
                    'yearly': 'year(s)'
                };
                document.getElementById('intervalUnit').textContent = units[eventData.recurrence_type] || 'week(s)';
            }

            // Load existing files
            loadEventFiles(eventData.id);

            // Show modal
            new bootstrap.Modal(document.getElementById('eventModal')).show();
        });
    });

    // Reset modal when creating new event
    document.querySelector('[data-bs-target="#eventModal"]').addEventListener('click', function() {
        // Reset modal title and form action
        document.getElementById('eventModalLabel').textContent = 'Create Event';
        document.getElementById('eventAction').value = 'create_event';
        document.getElementById('eventId').value = '';
        document.getElementById('submitButtonText').textContent = 'Create Event';

        // Reset form
        document.getElementById('eventForm').reset();
        document.getElementById('eventPublic').checked = true;

        // Clear existing files display
        document.getElementById('existingFiles').innerHTML = '';

        // Clear selected files for new events
        selectedFiles = [];
        document.getElementById('filePreviewArea').style.display = 'none';

        // Reset recurring options
        document.getElementById('recurringOptions').style.display = 'none';
        document.getElementById('intervalUnit').textContent = 'week(s)';
        document.getElementById('filePreviewList').innerHTML = '';
        document.getElementById('uploadProgress').style.display = 'none';
    });

    // Handle form submission for new events with files
    document.getElementById('eventForm').addEventListener('submit', function(e) {
        const eventAction = document.getElementById('eventAction').value;

        // Only intercept for new event creation when files are selected
        if (eventAction === 'create_event' && selectedFiles.length > 0) {
            e.preventDefault();

            // Show loading state
            const submitButton = document.getElementById('submitButton');
            const originalButtonText = submitButton.innerHTML;
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Creating...';

            document.getElementById('uploadProgress').style.display = 'block';

            // Create FormData from the form
            const formData = new FormData(this);

            // Validate required fields before sending
            const requiredFields = ['title', 'start_datetime', 'end_datetime', 'location'];
            const missingFields = [];

            for (const field of requiredFields) {
                const value = formData.get(field);
                if (!value || value.trim() === '') {
                    missingFields.push(field);
                }
            }

            if (missingFields.length > 0) {
                alert('Please fill in all required fields: ' + missingFields.join(', '));

                // Reset button state
                submitButton.disabled = false;
                submitButton.innerHTML = originalButtonText;
                document.getElementById('uploadProgress').style.display = 'none';
                return;
            }

            // Add all selected files
            if (selectedFiles.length > 0) {
                formData.append('has_promotional_files', '1');
                selectedFiles.forEach(file => {
                    formData.append('promotional_files[]', file);
                });
            }

            // Debug: Log form data before sending
            console.log('Form data being sent:');
            for (let [key, value] of formData.entries()) {
                console.log(key, value);
            }

            // Specifically check recurring and status fields
            console.log('Recurring checkbox checked:', document.getElementById('isRecurring').checked);
            console.log('Status dropdown value:', document.getElementById('eventStatus').value);

            // Send to our new endpoint
            fetch('create_event_with_materials.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin' // Ensure cookies/session are sent
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                // Check if response is ok
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // Get response text first to debug
                return response.text().then(text => {
                    console.log('Raw response:', text);
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('JSON parse error:', e);
                        console.error('Response text:', text);
                        throw new Error('Invalid JSON response: ' + text.substring(0, 100));
                    }
                });
            })
            .then(data => {
                console.log('Parsed data:', data);
                if (data.success) {
                    // Show success message
                    let message = 'Event created successfully';
                    if (data.uploaded_files.length > 0) {
                        message += ' with ' + data.uploaded_files.length + ' promotional materials';
                    }
                    if (data.upload_errors.length > 0) {
                        message += '\n\nSome files had issues:\n' + data.upload_errors.join('\n');
                    }
                    alert(message);

                    // Close modal
                    bootstrap.Modal.getInstance(document.getElementById('eventModal')).hide();

                    // Reload events list
                    location.reload();
                } else {
                    alert('Error creating event: ' + data.message);
                    submitButton.disabled = false;
                    submitButton.innerHTML = originalButtonText;
                    document.getElementById('uploadProgress').style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                console.error('Error details:', {
                    name: error.name,
                    message: error.message,
                    stack: error.stack
                });

                let errorMessage = 'An error occurred while creating the event: ' + error.message;

                // Check if it's a network error
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    errorMessage += '\n\nThis appears to be a network connectivity issue. Please:';
                    errorMessage += '\n1. Check your internet connection';
                    errorMessage += '\n2. Ensure the server is running';
                    errorMessage += '\n3. Try refreshing the page and logging in again';
                }

                alert(errorMessage);
                submitButton.disabled = false;
                submitButton.innerHTML = originalButtonText;
                document.getElementById('uploadProgress').style.display = 'none';
            });
        }
    });

    // View event functionality
    document.querySelectorAll('.view-event-btn').forEach(button => {
        button.addEventListener('click', function() {
            const eventData = JSON.parse(this.getAttribute('data-event'));

            // Populate view modal
            document.getElementById('viewEventTitle').textContent = eventData.title || '';
            document.getElementById('viewEventDescription').textContent = eventData.description || 'No description provided';

            // Format date and time
            const eventDate = new Date(eventData.event_date);
            let dateTimeText = eventDate.toLocaleString();
            document.getElementById('viewEventDateTime').textContent = dateTimeText;

            document.getElementById('viewEventLocation').textContent = eventData.location || 'No location specified';
            document.getElementById('viewEventCapacity').textContent = eventData.max_attendees || 'Unlimited';
            document.getElementById('viewEventCategory').textContent = 'No category';

            // Status badge
            const statusBadge = document.getElementById('viewEventStatus');
            const status = eventData.is_active == '1' ? 'Active' : 'Inactive';
            statusBadge.textContent = status;
            statusBadge.className = 'badge fs-6 bg-' + (eventData.is_active == '1' ? 'success' : 'secondary');

            // Requirements - hide for now since not in current table
            document.getElementById('viewEventRequirementsRow').style.display = 'none';

            // Registration and visibility - set defaults since not in current table
            document.getElementById('viewEventRegistration').textContent = 'Not required';
            document.getElementById('viewEventVisibility').textContent = 'Public';

            // Load and display files
            loadEventFilesForView(eventData.id);

            // Load and display header banner
            loadHeaderBannerForView(eventData.id);

            // Store event data for edit button
            document.getElementById('editFromViewBtn').setAttribute('data-event', JSON.stringify(eventData));

            new bootstrap.Modal(document.getElementById('viewEventModal')).show();
        });
    });

    // Edit from view modal
    document.getElementById('editFromViewBtn').addEventListener('click', function() {
        const eventData = JSON.parse(this.getAttribute('data-event'));

        // Close view modal
        bootstrap.Modal.getInstance(document.getElementById('viewEventModal')).hide();

        // Trigger edit functionality
        setTimeout(() => {
            const editBtn = document.querySelector(`.edit-event-btn[data-event*='"id":${eventData.id}']`);
            if (editBtn) {
                editBtn.click();
            }
        }, 300);
    });

    // Delete event functionality
    document.querySelectorAll('.delete-event-btn').forEach(button => {
        button.addEventListener('click', function() {
            const eventId = this.getAttribute('data-id');
            const eventTitle = this.getAttribute('data-title');

            document.getElementById('deleteEventId').value = eventId;
            document.getElementById('deleteEventTitle').textContent = eventTitle;

            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        });
    });

    // Form validation
    document.getElementById('eventForm').addEventListener('submit', function(e) {
        const startDate = new Date(document.getElementById('eventStartDate').value);
        const endDate = new Date(document.getElementById('eventEndDate').value);

        if (endDate <= startDate) {
            e.preventDefault();
            alert('End date must be after start date.');
            return false;
        }

        const regDeadline = document.getElementById('eventRegDeadline').value;
        if (regDeadline) {
            const deadlineDate = new Date(regDeadline);
            if (deadlineDate >= startDate) {
                e.preventDefault();
                alert('Registration deadline must be before the event start date.');
                return false;
            }
        }
    });

    // Function to load existing event files for editing
    function loadEventFiles(eventId) {
        // Load promotional materials
        loadPromotionalMaterials(eventId);

        // Load legacy PDF files
        fetch(`get_event_files.php?event_id=${eventId}`)
            .then(response => response.json())
            .then(files => {
                const container = document.getElementById('existingFiles');
                const pdfFiles = files.filter(file => file.file_type === 'application/pdf');

                if (pdfFiles.length > 0) {
                    let html = '<div class="mt-2"><strong>Existing Documents:</strong><ul class="list-unstyled mt-1">';
                    pdfFiles.forEach(file => {
                        html += `<li class="d-flex justify-content-between align-items-center mb-1">
                            <span><i class="bi bi-file-pdf text-danger"></i> ${file.file_name}</span>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteEventFile(${file.id})">
                                <i class="bi bi-trash"></i>
                            </button>
                        </li>`;
                    });
                    html += '</ul></div>';
                    container.innerHTML = html;
                } else {
                    container.innerHTML = '';
                }
            })
            .catch(error => {
                console.error('Error loading files:', error);
                document.getElementById('existingFiles').innerHTML = '';
            });
    }

    // Function to load event files for view modal
    function loadEventFilesForView(eventId) {
        // Load promotional materials and documents
        fetch(`get_promotional_materials.php?event_id=${eventId}`)
            .then(response => response.json())
            .then(data => {
                const container = document.getElementById('viewEventFiles');
                const row = document.getElementById('viewEventFilesRow');

                if (data.success && (data.promotional_materials.length > 0 || data.documents.length > 0)) {
                    let html = '';

                    // Display promotional materials
                    if (data.promotional_materials.length > 0) {
                        html += '<div class="mb-4">';
                        html += '<h6 class="text-muted mb-3"><i class="bi bi-images text-success"></i> Promotional Materials</h6>';
                        html += '<div class="row">';

                        data.promotional_materials.forEach(material => {
                            html += `<div class="col-md-4 col-lg-3 mb-3">
                                <div class="card border-0 shadow-sm h-100">`;

                            if (material.is_image) {
                                html += `<img src="${material.path}" class="card-img-top" style="height: 150px; object-fit: cover;" alt="${material.alt_text || material.name}">`;
                            } else {
                                html += `<div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 150px;">
                                    <i class="bi bi-file-earmark-pdf text-danger fs-1"></i>
                                </div>`;
                            }

                            html += `<div class="card-body p-2">
                                <p class="card-text small mb-2 fw-bold" title="${material.name}">${material.name.length > 20 ? material.name.substring(0, 20) + '...' : material.name}</p>
                                <div class="d-grid">
                                    <a href="${material.path}" class="btn btn-outline-success btn-sm" target="_blank" download="${material.name}">
                                        <i class="bi bi-download me-1"></i> Download
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>`;
                        });

                        html += '</div></div>';
                    }

                    // Display documents
                    if (data.documents.length > 0) {
                        html += '<div class="mb-3">';
                        html += '<h6 class="text-muted mb-3"><i class="bi bi-file-earmark-text text-primary"></i> Documents & Resources</h6>';
                        html += '<div class="row">';

                        data.documents.forEach(document => {
                            const fileExt = document.name.split('.').pop().toLowerCase();
                            let iconClass = 'bi-file-earmark';
                            let iconColor = 'text-secondary';

                            switch(fileExt) {
                                case 'pdf':
                                    iconClass = 'bi-file-earmark-pdf';
                                    iconColor = 'text-danger';
                                    break;
                                case 'doc':
                                case 'docx':
                                    iconClass = 'bi-file-earmark-word';
                                    iconColor = 'text-primary';
                                    break;
                                case 'xls':
                                case 'xlsx':
                                    iconClass = 'bi-file-earmark-excel';
                                    iconColor = 'text-success';
                                    break;
                                case 'ppt':
                                case 'pptx':
                                    iconClass = 'bi-file-earmark-ppt';
                                    iconColor = 'text-warning';
                                    break;
                            }

                            html += `<div class="col-md-6 mb-2">
                                <div class="card border-0 shadow-sm h-100">
                                    <div class="card-body p-3">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="bi ${iconClass} ${iconColor} me-2 fs-4"></i>
                                            <div class="flex-grow-1">
                                                <div class="fw-bold small" title="${document.name}">${document.name.length > 25 ? document.name.substring(0, 25) + '...' : document.name}</div>
                                                <small class="text-muted">${fileExt.toUpperCase()}</small>
                                            </div>
                                        </div>
                                        <div class="d-grid">
                                            <a href="${document.path}" class="btn btn-outline-primary btn-sm" target="_blank" download="${document.name}">
                                                <i class="bi bi-download me-1"></i> Download
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>`;
                        });

                        html += '</div></div>';
                    }

                    container.innerHTML = html;
                    row.style.display = 'block';
                } else {
                    // Fallback to legacy files if no promotional materials found
                    loadLegacyEventFiles(eventId);
                }
            })
            .catch(error => {
                console.error('Error loading promotional materials:', error);
                // Fallback to legacy files on error
                loadLegacyEventFiles(eventId);
            });
    }

    // Function to load legacy event files as fallback
    function loadLegacyEventFiles(eventId) {
        fetch(`get_event_files.php?event_id=${eventId}`)
            .then(response => response.json())
            .then(files => {
                const container = document.getElementById('viewEventFiles');
                const row = document.getElementById('viewEventFilesRow');

                if (files.length > 0) {
                    let html = '<div class="mb-3">';
                    html += '<h6 class="text-muted mb-3"><i class="bi bi-file-earmark-text text-primary"></i> Event Files</h6>';
                    html += '<ul class="list-unstyled">';
                    files.forEach(file => {
                        html += `<li class="mb-2">
                            <a href="${file.file_path}" target="_blank" class="text-decoration-none">
                                <i class="bi bi-file-pdf text-danger"></i> ${file.file_name}
                            </a>
                            <small class="text-muted ms-2">(${file.file_size_formatted})</small>
                        </li>`;
                    });
                    html += '</ul></div>';
                    container.innerHTML = html;
                    row.style.display = 'block';
                } else {
                    row.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error loading legacy files:', error);
                document.getElementById('viewEventFilesRow').style.display = 'none';
            });
    }

    // Function to load header banner for view modal
    function loadHeaderBannerForView(eventId) {
        fetch(`get_promotional_materials.php?event_id=${eventId}`)
            .then(response => response.json())
            .then(data => {
                const bannerRow = document.getElementById('viewEventBannerRow');
                const bannerImg = document.getElementById('viewEventBanner');

                if (data.success && data.header_banner) {
                    const banner = data.header_banner;

                    if (banner.is_image) {
                        bannerImg.src = banner.path;
                        bannerImg.alt = banner.alt_text || banner.name;
                        bannerRow.style.display = 'block';
                    } else {
                        // For PDF banners, show a placeholder or link
                        bannerRow.style.display = 'none';
                    }
                } else {
                    bannerRow.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error loading header banner:', error);
                document.getElementById('viewEventBannerRow').style.display = 'none';
            });
    }

    // Function to delete event file
    window.deleteEventFile = function(fileId) {
        if (confirm('Are you sure you want to delete this file?')) {
            fetch('delete_event_file.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ file_id: fileId })
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    // Reload files for current event
                    const eventId = document.getElementById('eventId').value;
                    if (eventId) {
                        loadEventFiles(eventId);
                    }
                } else {
                    alert('Error deleting file: ' + result.message);
                }
            })
            .catch(error => {
                console.error('Error deleting file:', error);
                alert('Error deleting file');
            });
        }
    };

    // Pagination JavaScript
    window.changePaginationLimit = function(newLimit) {
        const urlParams = new URLSearchParams(window.location.search);
        urlParams.set('limit', newLimit);
        urlParams.set('page', '1'); // Reset to first page
        window.location.href = 'events.php?' + urlParams.toString();
    };

    window.goToPage = function(pageNumber) {
        const totalPages = <?php echo isset($pagination['total_pages']) ? $pagination['total_pages'] : 1; ?>;
        pageNumber = parseInt(pageNumber);

        if (pageNumber < 1 || pageNumber > totalPages || isNaN(pageNumber)) {
            alert('Please enter a valid page number between 1 and ' + totalPages);
            return;
        }

        const urlParams = new URLSearchParams(window.location.search);
        urlParams.set('page', pageNumber);
        window.location.href = 'events.php?' + urlParams.toString();
    };

    // Function to load promotional materials
    function loadPromotionalMaterials(eventId) {
        fetch(`get_promotional_materials.php?event_id=${eventId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayPromotionalMaterials(data, eventId);
                    updateHeaderBannerSelection(data.promotional_materials, data.header_banner, eventId);
                }
            })
            .catch(error => {
                console.error('Error loading promotional materials:', error);
            });
    }

    // Function to display promotional materials
    function displayPromotionalMaterials(data, eventId) {
        const container = document.getElementById('existingPromotionalMaterials');

        if (data.promotional_materials.length === 0 && data.documents.length === 0) {
            container.innerHTML = '';
            return;
        }

        let html = '<div class="mt-3"><h6>Existing Promotional Materials</h6>';

        // Display promotional materials
        if (data.promotional_materials.length > 0) {
            html += '<div class="row g-2 mb-3">';
            data.promotional_materials.forEach(file => {
                const isHeaderBanner = file.is_header_banner;
                html += `
                    <div class="col-md-4">
                        <div class="card ${isHeaderBanner ? 'border-primary' : ''}">
                            ${file.is_image ?
                                `<img src="${file.thumbnail || file.path}" class="card-img-top" style="height: 120px; object-fit: cover;">` :
                                `<div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 120px;">
                                    <i class="bi bi-file-pdf fs-1 text-danger"></i>
                                </div>`
                            }
                            <div class="card-body p-2">
                                <small class="card-title">${file.name}</small>
                                ${isHeaderBanner ? '<span class="badge bg-primary">Header Banner</span>' : ''}
                                <div class="btn-group w-100 mt-1">
                                    ${!isHeaderBanner ?
                                        `<button class="btn btn-sm btn-outline-primary" onclick="setHeaderBanner(${file.id}, ${eventId})">
                                            Set as Banner
                                        </button>` : ''
                                    }
                                    <button class="btn btn-sm btn-outline-danger" onclick="deletePromotionalFile(${file.id}, ${eventId})">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
        }

        html += '</div>';
        container.innerHTML = html;
    }

    // Function to update header banner selection
    function updateHeaderBannerSelection(promotionalMaterials, currentBanner, eventId) {
        const container = document.getElementById('headerBannerSelection');

        if (promotionalMaterials.length === 0) {
            container.innerHTML = `
                <p class="text-muted mb-0">
                    <i class="bi bi-info-circle"></i>
                    Upload promotional materials above, then select one as the header banner
                </p>
            `;
            return;
        }

        let html = '<div class="row g-2">';
        promotionalMaterials.forEach(file => {
            const isSelected = currentBanner && currentBanner.id === file.id;
            html += `
                <div class="col-md-3">
                    <div class="card ${isSelected ? 'border-primary bg-primary bg-opacity-10' : ''}"
                         style="cursor: pointer;" onclick="setHeaderBanner(${file.id}, ${eventId})">
                        ${file.is_image ?
                            `<img src="${file.thumbnail || file.path}" class="card-img-top" style="height: 80px; object-fit: cover;">` :
                            `<div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 80px;">
                                <i class="bi bi-file-pdf fs-3 text-danger"></i>
                            </div>`
                        }
                        <div class="card-body p-1 text-center">
                            <small>${file.name}</small>
                            ${isSelected ? '<div class="badge bg-primary">Selected</div>' : ''}
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';

        container.innerHTML = html;
    }

    // Function to set header banner
    function setHeaderBanner(fileId, eventId) {
        fetch('set_header_banner.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                file_id: fileId,
                event_id: eventId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload promotional materials to update display
                loadPromotionalMaterials(eventId);
            } else {
                alert('Error setting header banner: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while setting the header banner.');
        });
    }

    // Function to delete promotional file
    function deletePromotionalFile(fileId, eventId) {
        if (confirm('Are you sure you want to delete this promotional material?')) {
            fetch('delete_event_file.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ file_id: fileId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload promotional materials
                    loadPromotionalMaterials(eventId);
                } else {
                    alert('Error deleting file: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while deleting the file.');
            });
        }
    }

    // Promotional file upload handling
    const fileInput = document.getElementById('promotionalFiles');
    const uploadArea = document.querySelector('.file-upload-area');

    if (fileInput && uploadArea) {
        // File input change handler
        fileInput.addEventListener('change', function(e) {
            handleFileSelection(e.target.files);
        });

        // Drag and drop handlers
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#007bff';
            uploadArea.style.backgroundColor = '#f8f9fa';
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#dee2e6';
            uploadArea.style.backgroundColor = 'transparent';
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#dee2e6';
            uploadArea.style.backgroundColor = 'transparent';

            const files = e.dataTransfer.files;
            handleFileSelection(files);
        });
    }

    // Simple function to trigger file input (always works)
    function triggerFileInput() {
        console.log('Direct file input trigger'); // Debug log
        const fileInput = document.getElementById('promotionalFiles');
        if (fileInput) {
            fileInput.click();
        } else {
            console.error('File input element not found');
        }
    }



    // Global variable to store selected files for new events
    let selectedFiles = [];

    // Function to handle file selection (for both new and existing events)
    function handleFileSelection(files) {
        console.log('handleFileSelection called with', files.length, 'files');

        const eventId = document.getElementById('eventId').value;

        if (eventId) {
            // Existing event - upload immediately
            handlePromotionalFileUpload(files);
        } else {
            // New event - store files for later upload
            selectedFiles = Array.from(files);
            displayFilePreview(selectedFiles);
        }
    }

    // Function to display file preview for new events
    function displayFilePreview(files) {
        const previewArea = document.getElementById('filePreviewArea');
        const previewList = document.getElementById('filePreviewList');

        if (files.length === 0) {
            previewArea.style.display = 'none';
            return;
        }

        previewArea.style.display = 'block';
        previewList.innerHTML = '';

        files.forEach((file, index) => {
            const col = document.createElement('div');
            col.className = 'col-md-6 col-lg-4';

            const fileCard = document.createElement('div');
            fileCard.className = 'card h-100';
            fileCard.innerHTML = `
                <div class="card-body p-2">
                    <div class="d-flex align-items-center">
                        <i class="bi ${getFileIcon(file.type)} fs-4 me-2 text-primary"></i>
                        <div class="flex-grow-1 min-w-0">
                            <div class="fw-bold text-truncate" title="${file.name}">${file.name}</div>
                            <small class="text-muted">${formatFileSize(file.size)}</small>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-danger ms-2"
                                onclick="removeSelectedFile(${index})" title="Remove">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>
                </div>
            `;

            col.appendChild(fileCard);
            previewList.appendChild(col);
        });
    }

    // Function to remove a selected file
    function removeSelectedFile(index) {
        selectedFiles.splice(index, 1);
        displayFilePreview(selectedFiles);

        // Update the file input
        const fileInput = document.getElementById('promotionalFiles');
        const dt = new DataTransfer();
        selectedFiles.forEach(file => dt.items.add(file));
        fileInput.files = dt.files;
    }

    // Function to get file icon based on type
    function getFileIcon(fileType) {
        if (fileType.startsWith('image/')) {
            return 'bi-image';
        } else if (fileType === 'application/pdf') {
            return 'bi-file-pdf';
        } else {
            return 'bi-file-earmark';
        }
    }

    // Function to format file size
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Function to handle promotional file upload (for existing events)
    function handlePromotionalFileUpload(files) {
        console.log('handlePromotionalFileUpload called with', files.length, 'files');

        const eventId = document.getElementById('eventId').value;
        console.log('Event ID for upload:', eventId);

        if (!eventId) {
            console.error('No event ID available for upload');
            return;
        }

        Array.from(files).forEach(file => {
            const formData = new FormData();
            formData.append('promotional_file', file);
            formData.append('event_id', eventId);
            formData.append('is_header_banner', false);

            fetch('upload_promotional_material.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload promotional materials
                    loadPromotionalMaterials(eventId);
                } else {
                    alert('Error uploading ' + file.name + ': ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while uploading ' + file.name);
            });
        });
    }

    // Auto-edit functionality for newly created events
    <?php if (isset($auto_edit_event_id)): ?>
    // Automatically open edit modal for the newly created event
    setTimeout(function() {
        // Find the edit button for the new event and trigger it
        const editButton = document.querySelector(`[data-event*='"id":<?php echo $auto_edit_event_id; ?>']`);
        if (editButton) {
            editButton.click();
        } else {
            // If button not found, reload the page to show the new event
            window.location.reload();
        }
    }, 1000);
    <?php endif; ?>
});
</script>

<style>
/* Enhanced event status indicators */
.event-row.past-event {
    background-color: #f8f9fa;
    opacity: 0.8;
}

.event-row.today-event {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}

.event-row.upcoming-event {
    background-color: #e7f3ff;
    border-left: 4px solid #0d6efd;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

.time-elapsed {
    font-style: italic;
    color: #6c757d;
}

/* Hover effects for event rows */
.event-row:hover {
    background-color: rgba(0,0,0,0.05);
    transition: background-color 0.2s ease;
}

.event-row.past-event:hover {
    background-color: rgba(108,117,125,0.1);
}

.event-row.today-event:hover {
    background-color: rgba(255,193,7,0.2);
}

.event-row.upcoming-event:hover {
    background-color: rgba(13,110,253,0.1);
}
</style>

<?php include 'includes/footer.php'; ?>
