<?php
/**
 * Session Reminders Cron Job
 * 
 * This script sends reminder notifications for sessions that are 24 hours away.
 * It should be run daily, preferably in the morning.
 * 
 * Cron job command (run daily at 8:00 AM):
 * 0 8 * * * wget -q -O /dev/null "https://YOUR_DOMAIN.COM/YOUR_PATH/cron/session_reminders.php?cron_key=fac_2024_secure_cron_8x9q2p5m"
 */

// Security check - only allow execution via cron or command line
$validCronKey = 'fac_2024_secure_cron_8x9q2p5m';
$providedKey = $_GET['cron_key'] ?? '';

if (php_sapi_name() !== 'cli' && $providedKey !== $validCronKey) {
    http_response_code(403);
    die('Access denied. This script can only be executed via cron job or command line.');
}

// Set execution time limit
set_time_limit(300); // 5 minutes

// Include required files
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../classes/SessionNotificationManager.php';

echo "🔔 Session Reminders Cron Job Started\n";
echo "=====================================\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // Initialize notification manager
    $sessionNotificationManager = new SessionNotificationManager($pdo);
    
    // Get sessions that are 24-48 hours away
    $stmt = $pdo->prepare("
        SELECT DISTINCT es.id as session_id, sa.member_id, es.session_title,
               es.start_datetime, m.first_name, m.last_name, m.email
        FROM event_sessions es
        JOIN session_attendance sa ON es.id = sa.session_id
        JOIN members m ON sa.member_id = m.id
        WHERE sa.attendance_status = 'registered'
        AND es.status = 'active'
        AND es.start_datetime BETWEEN NOW() + INTERVAL 12 HOUR AND NOW() + INTERVAL 48 HOUR
        AND m.email IS NOT NULL AND m.email != ''
        ORDER BY es.start_datetime ASC
    ");
    
    $stmt->execute();
    $upcomingSessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "📊 Found " . count($upcomingSessions) . " session registrations requiring reminders\n\n";
    
    if (empty($upcomingSessions)) {
        echo "✅ No session reminders to send at this time.\n";
        echo "Cron job completed successfully.\n";
        exit(0);
    }
    
    $successCount = 0;
    $errorCount = 0;
    $processedSessions = [];
    
    foreach ($upcomingSessions as $session) {
        $sessionId = $session['session_id'];
        $memberId = $session['member_id'];
        $memberName = $session['first_name'] . ' ' . $session['last_name'];
        
        // Check if we already processed this session-member combination
        $key = $sessionId . '-' . $memberId;
        if (in_array($key, $processedSessions)) {
            continue;
        }
        $processedSessions[] = $key;
        
        echo "📧 Sending reminder for '{$session['session_title']}' to {$memberName}...\n";
        
        try {
            $result = $sessionNotificationManager->sendSessionReminder($sessionId, $memberId);
            
            if ($result['success']) {
                echo "   ✅ Reminder sent successfully\n";
                $successCount++;
            } else {
                echo "   ⚠️ Reminder not sent: {$result['message']}\n";
                if (strpos($result['message'], 'Not in reminder window') === false) {
                    $errorCount++;
                }
            }
            
        } catch (Exception $e) {
            echo "   ❌ Error sending reminder: " . $e->getMessage() . "\n";
            $errorCount++;
        }
        
        // Small delay to prevent overwhelming the email system
        usleep(100000); // 0.1 second delay
    }
    
    echo "\n📈 Session Reminder Summary:\n";
    echo "============================\n";
    echo "✅ Successful reminders: $successCount\n";
    echo "❌ Failed reminders: $errorCount\n";
    echo "📊 Total processed: " . ($successCount + $errorCount) . "\n";
    
    // Log the cron job execution
    $stmt = $pdo->prepare("
        INSERT INTO cron_job_logs (job_name, status, details, executed_at)
        VALUES ('session_reminders', 'completed', ?, NOW())
    ");
    
    $details = json_encode([
        'successful_reminders' => $successCount,
        'failed_reminders' => $errorCount,
        'total_sessions_checked' => count($upcomingSessions)
    ]);
    
    try {
        $stmt->execute([$details]);
    } catch (Exception $e) {
        // Create the cron_job_logs table if it doesn't exist
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS cron_job_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                job_name VARCHAR(100) NOT NULL,
                status ENUM('started', 'completed', 'failed') NOT NULL,
                details TEXT,
                executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_job_name (job_name),
                INDEX idx_executed_at (executed_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ");
        
        // Try to log again
        $stmt->execute([$details]);
    }
    
    echo "\n🎉 Session reminders cron job completed successfully!\n";
    echo "Next run: Tomorrow at " . date('H:i') . "\n";
    
} catch (Exception $e) {
    echo "❌ CRITICAL ERROR in session reminders cron job:\n";
    echo $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    
    // Log the error
    error_log("Session reminders cron job failed: " . $e->getMessage());
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO cron_job_logs (job_name, status, details, executed_at)
            VALUES ('session_reminders', 'failed', ?, NOW())
        ");
        $stmt->execute([$e->getMessage()]);
    } catch (Exception $logError) {
        error_log("Failed to log cron job error: " . $logError->getMessage());
    }
    
    exit(1);
}

// Additional functions for session reminder management

/**
 * Check if a session reminder has already been sent
 */
function hasReminderBeenSent($pdo, $sessionId, $memberId) {
    try {
        $stmt = $pdo->prepare("
            SELECT id FROM session_notification_logs
            WHERE session_id = ? AND recipient_id = ? 
            AND notification_type = 'session_reminder'
            AND success = 1
            AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ");
        $stmt->execute([$sessionId, $memberId]);
        return $stmt->fetch() !== false;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Get session reminder statistics
 */
function getSessionReminderStats($pdo, $days = 30) {
    try {
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_reminders,
                SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_reminders,
                COUNT(DISTINCT session_id) as unique_sessions,
                COUNT(DISTINCT recipient_id) as unique_recipients
            FROM session_notification_logs
            WHERE notification_type = 'session_reminder'
            AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        ");
        $stmt->execute([$days]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        return [
            'total_reminders' => 0,
            'successful_reminders' => 0,
            'unique_sessions' => 0,
            'unique_recipients' => 0
        ];
    }
}

/**
 * Clean up old session reminder logs
 */
function cleanupOldReminderLogs($pdo, $daysToKeep = 90) {
    try {
        $stmt = $pdo->prepare("
            DELETE FROM session_notification_logs
            WHERE notification_type = 'session_reminder'
            AND created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
        ");
        $stmt->execute([$daysToKeep]);
        return $stmt->rowCount();
    } catch (Exception $e) {
        error_log("Failed to cleanup old reminder logs: " . $e->getMessage());
        return 0;
    }
}

echo "\n🧹 Cleaning up old reminder logs (older than 90 days)...\n";
$cleanedLogs = cleanupOldReminderLogs($pdo, 90);
echo "🗑️ Cleaned up $cleanedLogs old log entries\n";

echo "\n📊 Recent reminder statistics (last 30 days):\n";
$stats = getSessionReminderStats($pdo, 30);
echo "Total reminders sent: {$stats['total_reminders']}\n";
echo "Successful reminders: {$stats['successful_reminders']}\n";
echo "Unique sessions: {$stats['unique_sessions']}\n";
echo "Unique recipients: {$stats['unique_recipients']}\n";

if ($stats['total_reminders'] > 0) {
    $successRate = round(($stats['successful_reminders'] / $stats['total_reminders']) * 100, 2);
    echo "Success rate: {$successRate}%\n";
}

echo "\n✨ Session reminders cron job finished at " . date('Y-m-d H:i:s') . "\n";
?>
