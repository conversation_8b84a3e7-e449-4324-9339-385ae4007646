<?php
// Simple schema application script
require_once '../config.php';

echo "<h2>Applying Enhanced Event Management Schema</h2>";

try {
    // Apply schema changes one by one
    $changes = [
        // Add status management columns to events table
        "ALTER TABLE events ADD COLUMN IF NOT EXISTS status ENUM('draft', 'published', 'cancelled', 'completed', 'archived') DEFAULT 'draft' AFTER is_active",
        "ALTER TABLE events ADD COLUMN IF NOT EXISTS allow_late_registration TINYINT(1) DEFAULT 1 AFTER status",
        "ALTER TABLE events ADD COLUMN IF NOT EXISTS late_registration_cutoff_hours INT DEFAULT 0 AFTER allow_late_registration",
        "ALTER TABLE events ADD COLUMN IF NOT EXISTS cancellation_reason TEXT AFTER late_registration_cutoff_hours",
        "ALTER TABLE events ADD COLUMN IF NOT EXISTS cancelled_at TIMESTAMP NULL AFTER cancellation_reason",
        "ALTER TABLE events ADD COLUMN IF NOT EXISTS cancelled_by INT(11) NULL AFTER cancelled_at",
        "ALTER TABLE events ADD COLUMN IF NOT EXISTS archived_at TIMESTAMP NULL AFTER cancelled_by",
        "ALTER TABLE events ADD COLUMN IF NOT EXISTS archived_by INT(11) NULL AFTER archived_at",
        
        // Add recurring events columns
        "ALTER TABLE events ADD COLUMN IF NOT EXISTS is_recurring TINYINT(1) DEFAULT 0 AFTER archived_by",
        "ALTER TABLE events ADD COLUMN IF NOT EXISTS recurrence_type ENUM('none', 'daily', 'weekly', 'monthly', 'yearly') DEFAULT 'none' AFTER is_recurring",
        "ALTER TABLE events ADD COLUMN IF NOT EXISTS recurrence_interval INT DEFAULT 1 AFTER recurrence_type",
        "ALTER TABLE events ADD COLUMN IF NOT EXISTS recurrence_end_date DATE NULL AFTER recurrence_interval",
        "ALTER TABLE events ADD COLUMN IF NOT EXISTS recurrence_count INT NULL AFTER recurrence_end_date",
        "ALTER TABLE events ADD COLUMN IF NOT EXISTS parent_event_id INT(11) NULL AFTER recurrence_count",
        "ALTER TABLE events ADD COLUMN IF NOT EXISTS recurrence_data JSON AFTER parent_event_id",
        
        // Add indexes
        "ALTER TABLE events ADD INDEX IF NOT EXISTS idx_status (status)",
        "ALTER TABLE events ADD INDEX IF NOT EXISTS idx_allow_late_registration (allow_late_registration)",
        "ALTER TABLE events ADD INDEX IF NOT EXISTS idx_is_recurring (is_recurring)",
        "ALTER TABLE events ADD INDEX IF NOT EXISTS idx_recurrence_type (recurrence_type)",
        "ALTER TABLE events ADD INDEX IF NOT EXISTS idx_parent_event_id (parent_event_id)",
    ];
    
    $executed = 0;
    $errors = [];
    
    foreach ($changes as $sql) {
        try {
            $pdo->exec($sql);
            $executed++;
            echo "<p>✅ Executed: " . substr($sql, 0, 80) . "...</p>";
        } catch (PDOException $e) {
            $errors[] = $e->getMessage();
            echo "<p>⚠️ Error: " . $e->getMessage() . "</p>";
        }
    }
    
    // Create new tables
    $tables = [
        "event_status_history" => "
            CREATE TABLE IF NOT EXISTS event_status_history (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                event_id INT(11) NOT NULL,
                old_status ENUM('draft', 'published', 'cancelled', 'completed', 'archived'),
                new_status ENUM('draft', 'published', 'cancelled', 'completed', 'archived') NOT NULL,
                reason TEXT,
                changed_by INT(11) NOT NULL,
                changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_event_id (event_id),
                INDEX idx_new_status (new_status),
                INDEX idx_changed_by (changed_by),
                INDEX idx_changed_at (changed_at),
                FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
            )",
        
        "recurring_event_instances" => "
            CREATE TABLE IF NOT EXISTS recurring_event_instances (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                parent_event_id INT(11) NOT NULL,
                instance_event_id INT(11) NOT NULL,
                instance_date DATE NOT NULL,
                instance_number INT NOT NULL,
                is_exception TINYINT(1) DEFAULT 0,
                exception_reason TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_parent_event_id (parent_event_id),
                INDEX idx_instance_event_id (instance_event_id),
                INDEX idx_instance_date (instance_date),
                UNIQUE KEY unique_parent_instance (parent_event_id, instance_number),
                FOREIGN KEY (parent_event_id) REFERENCES events(id) ON DELETE CASCADE,
                FOREIGN KEY (instance_event_id) REFERENCES events(id) ON DELETE CASCADE
            )",
        
        "event_settings" => "
            CREATE TABLE IF NOT EXISTS event_settings (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                event_id INT(11) NOT NULL,
                setting_key VARCHAR(100) NOT NULL,
                setting_value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_event_id (event_id),
                INDEX idx_setting_key (setting_key),
                UNIQUE KEY unique_event_setting (event_id, setting_key),
                FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
            )",
        
        "backup_configurations" => "
            CREATE TABLE IF NOT EXISTS backup_configurations (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                backup_type ENUM('full', 'data_only', 'structure_only') DEFAULT 'full',
                format ENUM('sql', 'json') DEFAULT 'sql',
                schedule_type ENUM('manual', 'daily', 'weekly', 'monthly') DEFAULT 'manual',
                schedule_time TIME DEFAULT '02:00:00',
                schedule_day_of_week TINYINT DEFAULT 0,
                schedule_day_of_month TINYINT DEFAULT 1,
                retention_days INT DEFAULT 30,
                is_active TINYINT(1) DEFAULT 1,
                email_notifications TINYINT(1) DEFAULT 1,
                notification_emails TEXT,
                created_by INT(11) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_schedule_type (schedule_type),
                INDEX idx_is_active (is_active),
                INDEX idx_created_by (created_by)
            )",
        
        "backup_history" => "
            CREATE TABLE IF NOT EXISTS backup_history (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                configuration_id INT(11),
                backup_name VARCHAR(255) NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                file_size BIGINT,
                backup_type ENUM('full', 'data_only', 'structure_only') NOT NULL,
                format ENUM('sql', 'json') NOT NULL,
                status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending',
                error_message TEXT,
                started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                completed_at TIMESTAMP NULL,
                created_by INT(11),
                INDEX idx_configuration_id (configuration_id),
                INDEX idx_status (status),
                INDEX idx_started_at (started_at),
                INDEX idx_created_by (created_by),
                FOREIGN KEY (configuration_id) REFERENCES backup_configurations(id) ON DELETE SET NULL
            )"
    ];
    
    foreach ($tables as $table_name => $sql) {
        try {
            $pdo->exec($sql);
            $executed++;
            echo "<p>✅ Created table: $table_name</p>";
        } catch (PDOException $e) {
            $errors[] = $e->getMessage();
            echo "<p>⚠️ Error creating table $table_name: " . $e->getMessage() . "</p>";
        }
    }
    
    // Update existing events to have proper status
    try {
        $pdo->exec("
            UPDATE events 
            SET status = CASE 
                WHEN is_active = 1 AND event_date > NOW() THEN 'published'
                WHEN is_active = 1 AND event_date <= NOW() THEN 'completed'
                WHEN is_active = 0 THEN 'draft'
                ELSE 'draft'
            END
            WHERE status IS NULL OR status = ''
        ");
        echo "<p>✅ Updated existing events with proper status</p>";
        $executed++;
    } catch (PDOException $e) {
        echo "<p>⚠️ Error updating event statuses: " . $e->getMessage() . "</p>";
    }
    
    echo "<h3>✅ Schema application completed! Executed $executed operations with " . count($errors) . " errors.</h3>";
    
} catch (Exception $e) {
    echo "<h3>❌ Error applying schema: " . $e->getMessage() . "</h3>";
}

// Check schema status
echo "<hr><h3>Final Schema Status</h3>";
try {
    $stmt = $pdo->query("DESCRIBE events");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $required_columns = [
        'status', 'allow_late_registration', 'late_registration_cutoff_hours',
        'cancellation_reason', 'cancelled_at', 'cancelled_by',
        'archived_at', 'archived_by', 'is_recurring', 'recurrence_type'
    ];
    
    echo "<h4>Events Table Columns:</h4>";
    foreach ($required_columns as $col) {
        $exists = in_array($col, $columns);
        echo "<p>" . ($exists ? "✅" : "❌") . " $col</p>";
    }
    
    $tables_to_check = [
        'event_status_history', 'recurring_event_instances', 'event_settings',
        'backup_configurations', 'backup_history'
    ];
    
    echo "<h4>New Tables:</h4>";
    foreach ($tables_to_check as $table) {
        try {
            $stmt = $pdo->query("SELECT 1 FROM $table LIMIT 1");
            echo "<p>✅ $table</p>";
        } catch (PDOException $e) {
            echo "<p>❌ $table</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error checking schema: " . $e->getMessage() . "</p>";
}
?>
