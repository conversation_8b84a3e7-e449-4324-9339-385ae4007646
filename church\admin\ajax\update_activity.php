<?php
/**
 * Session Activity Update Endpoint
 * 
 * Updates the last activity timestamp to prevent session timeout
 */

// Include AJAX session handler for proper session management
require_once '../includes/ajax-session-handler.php';

// Validate admin session
$sessionValidation = validateAjaxAdminSession();

if (!$sessionValidation['valid']) {
    sendAjaxResponse([
        'success' => false,
        'status' => $sessionValidation['error'],
        'message' => $sessionValidation['message']
    ]);
}

// Update session activity (already done by validateAjaxAdminSession)
// Return success with remaining time
sendAjaxResponse([
    'success' => true,
    'message' => 'Session activity updated',
    'remainingTime' => $sessionValidation['remaining_time'],
    'admin_id' => $sessionValidation['admin_id']
]);
?>
