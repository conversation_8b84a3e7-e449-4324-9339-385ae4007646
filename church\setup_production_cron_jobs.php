<?php
require_once 'config.php';

echo "<h1>🚀 PRODUCTION CRON JOB SETUP</h1>\n";
echo "<p><strong>Purpose:</strong> Generate production-ready cron job commands and validate setup</p>\n";

global $pdo;

// Configuration - Dynamic for any domain
$cronKey = 'fac_2024_secure_cron_8x9q2p5m';

// Auto-detect current domain or use configured SITE_URL
if (defined('SITE_URL') && !empty(SITE_URL)) {
    $siteUrl = SITE_URL;
} else {
    // Auto-detect from current request
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'yourdomain.com';
    $siteUrl = $protocol . '://' . $host;
}

// Auto-detect base path from current script location
$currentPath = dirname($_SERVER['SCRIPT_NAME']);
$basePath = $currentPath; // Will be something like '/campaign/church'

echo "<h2>🔧 CONFIGURATION</h2>\n";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6;'>\n";
echo "<p><strong>Detected Site URL:</strong> " . htmlspecialchars($siteUrl) . "</p>\n";
echo "<p><strong>Detected Base Path:</strong> " . htmlspecialchars($basePath) . "</p>\n";
echo "<p><strong>Full Base URL:</strong> " . htmlspecialchars($siteUrl . $basePath) . "</p>\n";
echo "<p><strong>Cron Key:</strong> " . htmlspecialchars($cronKey) . "</p>\n";
echo "<p><strong>Example Domain:</strong> freedomassemblydb.online (your current domain)</p>\n";
echo "</div>\n";

// Validate configuration
$configIssues = [];
if (strpos($siteUrl, 'localhost') !== false || strpos($siteUrl, '127.0.0.1') !== false) {
    $configIssues[] = "Site URL uses localhost - update to your production domain (e.g., https://freedomassemblydb.online)";
}
if (!defined('SITE_URL') || empty(SITE_URL)) {
    $configIssues[] = "SITE_URL not properly defined in config.php - should be your production domain (e.g., https://freedomassemblydb.online)";
}

if (!empty($configIssues)) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; margin: 15px 0;'>\n";
    echo "<h3>⚠️ Configuration Issues:</h3>\n";
    foreach ($configIssues as $issue) {
        echo "<p>• $issue</p>\n";
    }
    echo "</div>\n";
}

echo "<h2>📋 PRODUCTION CRON JOB COMMANDS</h2>\n";

$cronJobs = [
    [
        'name' => 'Birthday Reminders',
        'file' => 'cron/birthday_reminders.php',
        'schedule' => '0 7 * * *',
        'description' => 'Daily at 7:00 AM - Send birthday emails and notifications',
        'priority' => 'CRITICAL'
    ],
    [
        'name' => 'Scheduled Email Processing',
        'file' => 'cron/process_scheduled_emails.php',
        'schedule' => '*/5 * * * *',
        'description' => 'Every 5 minutes - Process email campaigns',
        'priority' => 'CRITICAL'
    ],
    [
        'name' => 'Email Queue Processing',
        'file' => 'cron/process_email_queue.php',
        'schedule' => '*/5 * * * *',
        'description' => 'Every 5 minutes - Process queued emails',
        'priority' => 'CRITICAL'
    ],
    [
        'name' => 'Event Reminders',
        'file' => 'cron/event_reminders.php',
        'schedule' => '0 8 * * *',
        'description' => 'Daily at 8:00 AM - Send event reminder emails',
        'priority' => 'HIGH'
    ],
    [
        'name' => 'Session Reminders',
        'file' => 'cron/session_reminders.php',
        'schedule' => '0 9 * * *',
        'description' => 'Daily at 9:00 AM - Send session reminder notifications (24 hours before)',
        'priority' => 'HIGH'
    ],
    [
        'name' => 'Birthday Gift Processing',
        'file' => 'cron/process_birthday_gifts.php',
        'schedule' => '0 9 * * *',
        'description' => 'Daily at 9:00 AM - Process birthday gifts',
        'priority' => 'MEDIUM'
    ],
    [
        'name' => 'System Cleanup',
        'file' => 'cron/system_cleanup.php',
        'schedule' => '0 2 * * 0',
        'description' => 'Weekly on Sunday at 2:00 AM - System maintenance',
        'priority' => 'LOW'
    ]
];

echo "<h3>🎯 COPY THESE COMMANDS TO YOUR CRONTAB</h3>\n";
echo "<p><strong>Note:</strong> These commands are dynamically generated for your current domain. Works with any domain!</p>\n";
echo "<div style='background: #343a40; color: #fff; padding: 20px; margin: 15px 0; border-radius: 5px;'>\n";
echo "<pre style='margin: 0; white-space: pre-wrap;'>";

foreach ($cronJobs as $job) {
    $url = $siteUrl . $basePath . '/' . $job['file'] . '?cron_key=' . $cronKey;
    echo "# {$job['name']} ({$job['priority']} PRIORITY)\n";
    echo "# {$job['description']}\n";
    echo "{$job['schedule']} wget -q -O /dev/null \"$url\"\n\n";
}

echo "</pre>\n";
echo "</div>\n";

// Show example with freedomassemblydb.online
echo "<h4>📋 Example for freedomassemblydb.online:</h4>\n";
echo "<div style='background: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; border-radius: 5px;'>\n";
echo "<pre style='margin: 0; white-space: pre-wrap; font-size: 12px;'>";

$exampleSiteUrl = 'https://freedomassemblydb.online';
foreach ($cronJobs as $job) {
    $exampleUrl = $exampleSiteUrl . $basePath . '/' . $job['file'] . '?cron_key=' . $cronKey;
    echo "# {$job['name']}\n";
    echo "{$job['schedule']} wget -q -O /dev/null \"$exampleUrl\"\n\n";
}

echo "</pre>\n";
echo "</div>\n";

echo "<h3>📱 Individual Test URLs</h3>\n";
echo "<p>Use these URLs to test each cron job manually:</p>\n";

foreach ($cronJobs as $job) {
    $url = $siteUrl . $basePath . '/' . $job['file'] . '?cron_key=' . $cronKey;
    $priorityColor = $job['priority'] === 'CRITICAL' ? '#dc3545' : 
                    ($job['priority'] === 'HIGH' ? '#fd7e14' : 
                    ($job['priority'] === 'MEDIUM' ? '#ffc107' : '#28a745'));
    
    echo "<div style='border-left: 4px solid $priorityColor; padding: 10px; margin: 10px 0; background: #f8f9fa;'>\n";
    echo "<h4 style='margin: 0 0 5px 0; color: $priorityColor;'>{$job['name']}</h4>\n";
    echo "<p style='margin: 0 0 5px 0; font-size: 14px;'>{$job['description']}</p>\n";
    echo "<p style='margin: 0;'><a href='$url' target='_blank' style='font-family: monospace; font-size: 12px;'>$url</a></p>\n";
    echo "</div>\n";
}

echo "<h2>🔍 SYSTEM VALIDATION</h2>\n";

// Check if cron files exist
echo "<h3>📁 File Validation</h3>\n";
$allFilesExist = true;
foreach ($cronJobs as $job) {
    $filePath = __DIR__ . '/' . $job['file'];
    if (file_exists($filePath)) {
        echo "<p style='color: green;'>✅ {$job['file']} - EXISTS</p>\n";
    } else {
        echo "<p style='color: red;'>❌ {$job['file']} - MISSING</p>\n";
        $allFilesExist = false;
    }
}

// Check database tables
echo "<h3>🗄️ Database Validation</h3>\n";
$requiredTables = [
    'members', 'email_templates', 'events', 'event_rsvps', 
    'email_schedules', 'email_queue', 'automated_emails_settings'
];

$allTablesExist = true;
foreach ($requiredTables as $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✅ Table '$table' - EXISTS</p>\n";
        } else {
            echo "<p style='color: red;'>❌ Table '$table' - MISSING</p>\n";
            $allTablesExist = false;
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Table '$table' - ERROR</p>\n";
        $allTablesExist = false;
    }
}

// Overall status
echo "<h3>📊 Overall Status</h3>\n";
if ($allFilesExist && $allTablesExist && empty($configIssues)) {
    echo "<div style='background: #d4edda; padding: 20px; border: 1px solid #c3e6cb; border-radius: 5px;'>\n";
    echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>🎉 READY FOR PRODUCTION!</h4>\n";
    echo "<p style='margin: 0;'>All files exist, database tables are present, and configuration looks good.</p>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #f8d7da; padding: 20px; border: 1px solid #f5c6cb; border-radius: 5px;'>\n";
    echo "<h4 style='color: #721c24; margin: 0 0 10px 0;'>⚠️ ISSUES FOUND</h4>\n";
    echo "<p style='margin: 0;'>Please resolve the issues above before deploying to production.</p>\n";
    echo "</div>\n";
}

echo "<h2>📖 DEPLOYMENT INSTRUCTIONS</h2>\n";

echo "<div style='background: #e7f3ff; padding: 20px; border: 1px solid #b3d9ff; border-radius: 5px;'>\n";
echo "<h3>🚀 Step-by-Step Deployment:</h3>\n";
echo "<ol>\n";
echo "<li><strong>Update Configuration:</strong>\n";
echo "   <ul>\n";
echo "   <li>Set SITE_URL in config.php to your production domain:<br>\n";
echo "       <code>define('SITE_URL', 'https://yourdomain.com');</code><br>\n";
echo "       <em>Example: <code>define('SITE_URL', 'https://freedomassemblydb.online');</code></em></li>\n";
echo "   <li>Verify database connection settings</li>\n";
echo "   </ul>\n";
echo "</li>\n";
echo "<li><strong>Upload Files:</strong>\n";
echo "   <ul>\n";
echo "   <li>Ensure all cron files are uploaded to your server</li>\n";
echo "   <li>Set proper file permissions (644 for PHP files)</li>\n";
echo "   </ul>\n";
echo "</li>\n";
echo "<li><strong>Set Up Cron Jobs:</strong>\n";
echo "   <ul>\n";
echo "   <li>Access your hosting control panel or SSH</li>\n";
echo "   <li>Add the cron commands from above</li>\n";
echo "   <li>Verify cron jobs are active</li>\n";
echo "   </ul>\n";
echo "</li>\n";
echo "<li><strong>Test Each Cron Job:</strong>\n";
echo "   <ul>\n";
echo "   <li>Visit each test URL manually</li>\n";
echo "   <li>Check for successful execution</li>\n";
echo "   <li>Monitor log files for errors</li>\n";
echo "   </ul>\n";
echo "</li>\n";
echo "<li><strong>Monitor for 24 Hours:</strong>\n";
echo "   <ul>\n";
echo "   <li>Check log files regularly</li>\n";
echo "   <li>Verify emails are being sent</li>\n";
echo "   <li>Ensure no errors in execution</li>\n";
echo "   </ul>\n";
echo "</li>\n";
echo "</ol>\n";
echo "</div>\n";

echo "<h2>🛠️ TROUBLESHOOTING</h2>\n";

echo "<div style='background: #fff3cd; padding: 20px; border: 1px solid #ffeaa7; border-radius: 5px;'>\n";
echo "<h3>🔧 Common Issues & Solutions:</h3>\n";
echo "<ul>\n";
echo "<li><strong>Cron jobs not running:</strong>\n";
echo "   <ul>\n";
echo "   <li>Check cron service is active: <code>systemctl status cron</code></li>\n";
echo "   <li>Verify crontab syntax: <code>crontab -l</code></li>\n";
echo "   <li>Check server timezone settings</li>\n";
echo "   </ul>\n";
echo "</li>\n";
echo "<li><strong>403 Forbidden errors:</strong>\n";
echo "   <ul>\n";
echo "   <li>Verify cron key is correct</li>\n";
echo "   <li>Check file permissions</li>\n";
echo "   <li>Ensure .htaccess allows access</li>\n";
echo "   </ul>\n";
echo "</li>\n";
echo "<li><strong>Emails not sending:</strong>\n";
echo "   <ul>\n";
echo "   <li>Check email settings in admin panel</li>\n";
echo "   <li>Verify SMTP configuration</li>\n";
echo "   <li>Check email logs for errors</li>\n";
echo "   </ul>\n";
echo "</li>\n";
echo "<li><strong>Database connection errors:</strong>\n";
echo "   <ul>\n";
echo "   <li>Verify config.php database settings</li>\n";
echo "   <li>Check database server status</li>\n";
echo "   <li>Ensure database user has proper permissions</li>\n";
echo "   </ul>\n";
echo "</li>\n";
echo "</ul>\n";
echo "</div>\n";

echo "<h2>📞 SUPPORT & MONITORING</h2>\n";

echo "<div style='background: #f8f9fa; padding: 20px; border: 1px solid #dee2e6; border-radius: 5px;'>\n";
echo "<h3>📊 Log File Locations:</h3>\n";
echo "<ul>\n";
echo "<li><strong>Birthday Reminders:</strong> logs/birthday_reminders.log</li>\n";
echo "<li><strong>Scheduled Emails:</strong> logs/scheduled_emails.log</li>\n";
echo "<li><strong>Email Queue:</strong> logs/email_queue.log</li>\n";
echo "<li><strong>Event Reminders:</strong> logs/event_reminders.log</li>\n";
echo "<li><strong>System Cleanup:</strong> logs/system_cleanup.log</li>\n";
echo "</ul>\n";

echo "<h3>🔍 Monitoring Commands:</h3>\n";
echo "<pre style='background: #343a40; color: #fff; padding: 10px; border-radius: 3px;'>";
echo "# Check cron status\n";
echo "crontab -l\n";
echo "systemctl status cron\n\n";
echo "# Monitor logs\n";
echo "tail -f logs/birthday_reminders.log\n";
echo "tail -f logs/scheduled_emails.log\n\n";
echo "# Check system logs\n";
echo "tail -f /var/log/cron\n";
echo "</pre>\n";
echo "</div>\n";

?>
