<?php
/**
 * Fix Event File Paths
 * 
 * This script corrects file paths in the event_files table to ensure
 * they point to the correct location in the uploads directory
 */

require_once 'config.php';

echo "<h1>🔧 Fix Event File Paths</h1>\n";
echo "<p>Correcting file paths in the database...</p>\n";

try {
    // Get all event files
    $stmt = $pdo->prepare("SELECT id, file_name, file_path, file_category FROM event_files ORDER BY id");
    $stmt->execute();
    $files = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>📊 Found " . count($files) . " files to check</h2>\n";
    
    $fixedCount = 0;
    $alreadyCorrectCount = 0;
    $errorCount = 0;
    
    foreach ($files as $file) {
        $originalPath = $file['file_path'];
        $fileName = $file['file_name'];
        $fileId = $file['id'];
        $category = $file['file_category'];
        
        // Determine the correct path based on file type and category
        $correctPath = '';
        
        if ($category === 'promotional') {
            $correctPath = 'uploads/events/promotional/' . basename($originalPath);
        } elseif ($category === 'banner') {
            $correctPath = 'uploads/events/banners/' . basename($originalPath);
        } else {
            // Default to events directory
            $correctPath = 'uploads/events/' . basename($originalPath);
        }
        
        // Check if the file actually exists at the correct location
        $fileExists = file_exists($correctPath);
        
        if (!$fileExists) {
            // Try alternative locations
            $alternativePaths = [
                'uploads/events/' . basename($originalPath),
                'uploads/' . basename($originalPath),
                $originalPath
            ];
            
            foreach ($alternativePaths as $altPath) {
                if (file_exists($altPath)) {
                    $correctPath = $altPath;
                    $fileExists = true;
                    break;
                }
            }
        }
        
        // Check if path needs updating
        if ($originalPath === $correctPath) {
            $alreadyCorrectCount++;
            echo "<p style='color: green;'>✅ File ID $fileId: Path already correct ($correctPath)</p>\n";
        } else {
            if ($fileExists) {
                // Update the database
                try {
                    $updateStmt = $pdo->prepare("UPDATE event_files SET file_path = ? WHERE id = ?");
                    $updateStmt->execute([$correctPath, $fileId]);
                    
                    $fixedCount++;
                    echo "<p style='color: blue;'>🔧 File ID $fileId: Updated path</p>\n";
                    echo "<p style='margin-left: 20px;'>From: <code>$originalPath</code></p>\n";
                    echo "<p style='margin-left: 20px;'>To: <code>$correctPath</code></p>\n";
                    
                } catch (Exception $e) {
                    $errorCount++;
                    echo "<p style='color: red;'>❌ File ID $fileId: Database update failed - " . $e->getMessage() . "</p>\n";
                }
            } else {
                $errorCount++;
                echo "<p style='color: orange;'>⚠️ File ID $fileId: File not found at any location</p>\n";
                echo "<p style='margin-left: 20px;'>Original: <code>$originalPath</code></p>\n";
                echo "<p style='margin-left: 20px;'>Expected: <code>$correctPath</code></p>\n";
                echo "<p style='margin-left: 20px;'>File: <code>$fileName</code></p>\n";
            }
        }
    }
    
    echo "<hr>\n";
    echo "<h2>📈 Summary</h2>\n";
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px;'>\n";
    echo "<p><strong>Total files processed:</strong> " . count($files) . "</p>\n";
    echo "<p style='color: green;'><strong>Already correct:</strong> $alreadyCorrectCount</p>\n";
    echo "<p style='color: blue;'><strong>Fixed:</strong> $fixedCount</p>\n";
    echo "<p style='color: red;'><strong>Errors:</strong> $errorCount</p>\n";
    echo "</div>\n";
    
    if ($fixedCount > 0) {
        echo "<h2>✅ File Path Fix Complete!</h2>\n";
        echo "<p>$fixedCount file paths have been corrected in the database.</p>\n";
        echo "<p>Event materials should now be visible and downloadable.</p>\n";
    }
    
    if ($errorCount > 0) {
        echo "<h2>⚠️ Some Issues Found</h2>\n";
        echo "<p>$errorCount files could not be fixed. These files may be missing from the server.</p>\n";
        echo "<p>You may need to re-upload these files through the admin panel.</p>\n";
    }
    
    // Test a few file URLs
    echo "<h2>🧪 File Access Test</h2>\n";
    $testFiles = array_slice($files, 0, 3); // Test first 3 files
    
    foreach ($testFiles as $file) {
        $stmt = $pdo->prepare("SELECT file_path FROM event_files WHERE id = ?");
        $stmt->execute([$file['id']]);
        $updatedFile = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $filePath = $updatedFile['file_path'];
        $fileExists = file_exists($filePath);
        $fileUrl = 'http://localhost/campaign/church/' . $filePath;
        
        echo "<p><strong>File:</strong> {$file['file_name']}</p>\n";
        echo "<p><strong>Path:</strong> $filePath</p>\n";
        echo "<p><strong>Exists:</strong> " . ($fileExists ? '✅ Yes' : '❌ No') . "</p>\n";
        echo "<p><strong>URL:</strong> <a href='$fileUrl' target='_blank'>$fileUrl</a></p>\n";
        echo "<hr>\n";
    }
    
    echo "<h2>🎯 Next Steps</h2>\n";
    echo "<ul>\n";
    echo "<li>✅ Test event detail pages to ensure materials are visible</li>\n";
    echo "<li>✅ Test download functionality for documents</li>\n";
    echo "<li>✅ Check promotional materials display correctly</li>\n";
    echo "<li>✅ If files are still missing, re-upload them through the admin panel</li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>💥 Critical Error</h2>\n";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>Stack trace:</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

code {
    background: #f4f4f4;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}

hr {
    margin: 20px 0;
    border: none;
    border-top: 1px solid #ddd;
}

h1, h2 {
    color: #333;
}

.summary-box {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
}
</style>
