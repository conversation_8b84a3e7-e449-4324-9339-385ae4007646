<?php
session_start();
require_once 'includes/config.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    echo "<div class='alert alert-danger'>Please log in as admin first!</div>";
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Creation Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
        }
        .result-area {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-top: 1rem;
            min-height: 100px;
        }
        .file-preview {
            border: 2px dashed #dee2e6;
            border-radius: 0.375rem;
            padding: 2rem;
            text-align: center;
            margin: 1rem 0;
        }
        .file-preview.dragover {
            border-color: #0d6efd;
            background-color: #f0f8ff;
        }
        .selected-files {
            margin-top: 1rem;
        }
        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">Event Creation Test</h1>
        
        <!-- Session Info -->
        <div class="test-section">
            <h3>Session Information</h3>
            <div class="alert alert-info">
                <strong>Admin ID:</strong> <?php echo $_SESSION['admin_id']; ?><br>
                <strong>Admin Name:</strong> <?php echo $_SESSION['admin_name'] ?? 'Not set'; ?><br>
                <strong>Session ID:</strong> <?php echo session_id(); ?>
            </div>
        </div>

        <!-- Test Event Creation Form -->
        <div class="test-section">
            <h3>Test Event Creation</h3>
            <form id="testEventForm" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="title" class="form-label">Event Title *</label>
                            <input type="text" class="form-control" id="title" name="title" value="Test Event <?php echo date('Y-m-d H:i:s'); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3">This is a test event created on <?php echo date('Y-m-d H:i:s'); ?></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="start_datetime" class="form-label">Start Date & Time *</label>
                            <input type="datetime-local" class="form-control" id="start_datetime" name="start_datetime" value="<?php echo date('Y-m-d\TH:i', strtotime('+1 day')); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="end_datetime" class="form-label">End Date & Time</label>
                            <input type="datetime-local" class="form-control" id="end_datetime" name="end_datetime" value="<?php echo date('Y-m-d\TH:i', strtotime('+1 day +2 hours')); ?>">
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="location" class="form-label">Location *</label>
                            <input type="text" class="form-control" id="location" name="location" value="Test Location" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="capacity" class="form-label">Capacity</label>
                            <input type="number" class="form-control" id="capacity" name="capacity" value="50">
                        </div>
                        
                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="draft">Draft</option>
                                <option value="published" selected>Published</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_recurring" name="is_recurring">
                                <label class="form-check-label" for="is_recurring">
                                    Recurring Event
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recurring Event Options -->
                <div id="recurringOptions" style="display: none;">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="recurrence_type" class="form-label">Recurrence Type</label>
                                <select class="form-control" id="recurrence_type" name="recurrence_type">
                                    <option value="daily">Daily</option>
                                    <option value="weekly" selected>Weekly</option>
                                    <option value="monthly">Monthly</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="recurrence_interval" class="form-label">Interval</label>
                                <input type="number" class="form-control" id="recurrence_interval" name="recurrence_interval" value="1" min="1">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="recurrence_count" class="form-label">Number of Occurrences</label>
                                <input type="number" class="form-control" id="recurrence_count" name="recurrence_count" value="5" min="1">
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Promotional Materials -->
                <div class="mb-3">
                    <label class="form-label">Promotional Materials</label>
                    <div class="file-preview" id="fileDropArea">
                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                        <p>Click to upload or drag and drop files here</p>
                        <p class="text-muted small">Images (JPG, PNG, GIF) and PDFs • Max 15MB each</p>
                        <input type="file" id="promotional_files" name="promotional_files[]" multiple accept="image/*,.pdf" style="display: none;">
                        <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('promotional_files').click();">
                            Browse Files
                        </button>
                    </div>
                    <div id="selectedFiles" class="selected-files"></div>
                </div>
                
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <span id="submitText">Create Test Event</span>
                        <span id="submitSpinner" class="spinner-border spinner-border-sm ms-2" style="display: none;"></span>
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="clearForm()">Clear Form</button>
                    <button type="button" class="btn btn-info" onclick="testSessionOnly()">Test Session Only</button>
                </div>
            </form>
            
            <div id="testResult" class="result-area">
                <em>Test results will appear here...</em>
            </div>
        </div>
        
        <!-- Recent Events -->
        <div class="test-section">
            <h3>Recent Test Events</h3>
            <div id="recentEvents" class="result-area">
                <button class="btn btn-outline-secondary" onclick="loadRecentEvents()">Load Recent Events</button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedFiles = [];
        
        // Show/hide recurring options
        document.getElementById('is_recurring').addEventListener('change', function() {
            document.getElementById('recurringOptions').style.display = this.checked ? 'block' : 'none';
        });
        
        // File handling
        const fileInput = document.getElementById('promotional_files');
        const fileDropArea = document.getElementById('fileDropArea');
        const selectedFilesDiv = document.getElementById('selectedFiles');
        
        fileInput.addEventListener('change', handleFileSelect);
        
        // Drag and drop
        fileDropArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });
        
        fileDropArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });
        
        fileDropArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            const files = e.dataTransfer.files;
            handleFiles(files);
        });
        
        function handleFileSelect(e) {
            handleFiles(e.target.files);
        }
        
        function handleFiles(files) {
            selectedFiles = Array.from(files);
            displaySelectedFiles();
        }
        
        function displaySelectedFiles() {
            if (selectedFiles.length === 0) {
                selectedFilesDiv.innerHTML = '';
                return;
            }
            
            let html = '<h6>Selected Files:</h6>';
            selectedFiles.forEach((file, index) => {
                html += `
                    <div class="file-item">
                        <span><strong>${file.name}</strong> (${(file.size / 1024 / 1024).toFixed(2)} MB)</span>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile(${index})">Remove</button>
                    </div>
                `;
            });
            selectedFilesDiv.innerHTML = html;
        }
        
        function removeFile(index) {
            selectedFiles.splice(index, 1);
            displaySelectedFiles();
        }
        
        function clearForm() {
            document.getElementById('testEventForm').reset();
            selectedFiles = [];
            displaySelectedFiles();
            document.getElementById('recurringOptions').style.display = 'none';
            document.getElementById('testResult').innerHTML = '<em>Test results will appear here...</em>';
        }
        
        // Test session only
        function testSessionOnly() {
            fetch('test_session.php', {
                method: 'POST',
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('testResult').innerHTML = 
                    '<h5>Session Test Result:</h5><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                document.getElementById('testResult').innerHTML = 
                    '<div class="alert alert-danger">Session Test Error: ' + error.message + '</div>';
            });
        }
        
        // Main form submission
        document.getElementById('testEventForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const submitText = document.getElementById('submitText');
            const submitSpinner = document.getElementById('submitSpinner');
            
            // Show loading state
            submitBtn.disabled = true;
            submitText.textContent = 'Creating...';
            submitSpinner.style.display = 'inline-block';
            
            // Create FormData
            const formData = new FormData(this);
            
            // Add selected files
            selectedFiles.forEach(file => {
                formData.append('promotional_files[]', file);
            });
            
            // Log form data for debugging
            console.log('Submitting form data:');
            for (let [key, value] of formData.entries()) {
                console.log(key + ':', value);
            }
            
            // Submit to create_event_with_materials.php
            fetch('create_event_with_materials.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.text();
            })
            .then(text => {
                console.log('Raw response:', text);
                try {
                    const data = JSON.parse(text);
                    displayResult(data, true);
                } catch (e) {
                    displayResult({
                        success: false,
                        message: 'Invalid JSON response',
                        raw_response: text
                    }, false);
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                displayResult({
                    success: false,
                    message: 'Network error: ' + error.message
                }, false);
            })
            .finally(() => {
                // Reset button state
                submitBtn.disabled = false;
                submitText.textContent = 'Create Test Event';
                submitSpinner.style.display = 'none';
            });
        });
        
        function displayResult(data, isJson) {
            let html = '<h5>Test Result:</h5>';
            
            if (isJson && data.success) {
                html += '<div class="alert alert-success">✅ Event created successfully!</div>';
                if (data.event_id) {
                    html += '<p><strong>Event ID:</strong> ' + data.event_id + '</p>';
                }
                if (data.uploaded_files && data.uploaded_files.length > 0) {
                    html += '<p><strong>Uploaded Files:</strong> ' + data.uploaded_files.length + '</p>';
                }
            } else {
                html += '<div class="alert alert-danger">❌ Event creation failed</div>';
            }
            
            html += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            document.getElementById('testResult').innerHTML = html;
            
            // Auto-load recent events if successful
            if (isJson && data.success) {
                setTimeout(loadRecentEvents, 1000);
            }
        }
        
        function loadRecentEvents() {
            // Simple query to get recent events
            fetch('test_session.php', {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=get_recent_events'
            })
            .then(response => response.json())
            .then(data => {
                let html = '<h6>Recent Events:</h6>';
                if (data.success && data.events) {
                    data.events.forEach(event => {
                        html += `
                            <div class="border p-2 mb-2 rounded">
                                <strong>${event.title}</strong><br>
                                <small>ID: ${event.id} | Status: ${event.status} | Created: ${event.created_at}</small>
                            </div>
                        `;
                    });
                } else {
                    html += '<p class="text-muted">No recent events found or error loading events.</p>';
                }
                document.getElementById('recentEvents').innerHTML = html;
            })
            .catch(error => {
                document.getElementById('recentEvents').innerHTML = 
                    '<div class="alert alert-warning">Error loading recent events: ' + error.message + '</div>';
            });
        }
        
        // Auto-load recent events on page load
        window.addEventListener('load', loadRecentEvents);
    </script>
</body>
</html>
