<?php
/**
 * Session Notification Logger
 * 
 * Handles logging of session notification activities for tracking and debugging
 */

class SessionNotificationLogger {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->ensureLogTable();
    }
    
    /**
     * Log a notification event
     */
    public function logNotification($notificationType, $sessionId, $recipientId, $success, $details = null) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO session_notification_logs (
                    notification_type, session_id, recipient_id, recipient_type,
                    success, details, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $recipientType = $recipientId ? 'member' : 'guest';
            
            $stmt->execute([
                $notificationType,
                $sessionId,
                $recipientId,
                $recipientType,
                $success ? 1 : 0,
                $details
            ]);
            
        } catch (Exception $e) {
            error_log("Failed to log session notification: " . $e->getMessage());
        }
    }
    
    /**
     * Log bulk notification results
     */
    public function logBulkNotification($notificationType, $sessionId, $successCount, $errorCount) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO session_notification_logs (
                    notification_type, session_id, recipient_id, recipient_type,
                    success, details, created_at
                ) VALUES (?, ?, NULL, 'bulk', 1, ?, NOW())
            ");
            
            $details = json_encode([
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'total_recipients' => $successCount + $errorCount
            ]);
            
            $stmt->execute([
                $notificationType,
                $sessionId,
                $details
            ]);
            
        } catch (Exception $e) {
            error_log("Failed to log bulk notification: " . $e->getMessage());
        }
    }
    
    /**
     * Log notification errors
     */
    public function logError($notificationType, $sessionId, $recipientId, $errorMessage) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO session_notification_logs (
                    notification_type, session_id, recipient_id, recipient_type,
                    success, error_message, created_at
                ) VALUES (?, ?, ?, ?, 0, ?, NOW())
            ");
            
            $recipientType = $recipientId ? 'member' : 'system';
            
            $stmt->execute([
                $notificationType,
                $sessionId,
                $recipientId,
                $recipientType,
                $errorMessage
            ]);
            
        } catch (Exception $e) {
            error_log("Failed to log notification error: " . $e->getMessage());
        }
    }
    
    /**
     * Get notification statistics for a session
     */
    public function getSessionNotificationStats($sessionId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT 
                    notification_type,
                    COUNT(*) as total_notifications,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful,
                    SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed
                FROM session_notification_logs
                WHERE session_id = ?
                GROUP BY notification_type
                ORDER BY notification_type
            ");
            
            $stmt->execute([$sessionId]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Failed to get notification stats: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get recent notification activity
     */
    public function getRecentActivity($limit = 50) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT 
                    snl.*,
                    es.session_title,
                    e.title as event_title,
                    m.first_name, m.last_name, m.email
                FROM session_notification_logs snl
                LEFT JOIN event_sessions es ON snl.session_id = es.id
                LEFT JOIN events e ON es.event_id = e.id
                LEFT JOIN members m ON snl.recipient_id = m.id
                ORDER BY snl.created_at DESC
                LIMIT ?
            ");
            
            $stmt->execute([$limit]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Failed to get recent activity: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Clean up old logs (older than specified days)
     */
    public function cleanupOldLogs($daysToKeep = 90) {
        try {
            $stmt = $this->pdo->prepare("
                DELETE FROM session_notification_logs 
                WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
            ");
            
            $stmt->execute([$daysToKeep]);
            return $stmt->rowCount();
            
        } catch (Exception $e) {
            error_log("Failed to cleanup old logs: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Ensure the log table exists
     */
    private function ensureLogTable() {
        try {
            $this->pdo->exec("
                CREATE TABLE IF NOT EXISTS session_notification_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    notification_type VARCHAR(50) NOT NULL,
                    session_id INT NOT NULL,
                    recipient_id INT NULL,
                    recipient_type ENUM('member', 'guest', 'admin', 'bulk', 'system') NOT NULL,
                    success TINYINT(1) NOT NULL DEFAULT 0,
                    details TEXT NULL,
                    error_message TEXT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_session_id (session_id),
                    INDEX idx_notification_type (notification_type),
                    INDEX idx_created_at (created_at),
                    INDEX idx_success (success)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
            ");
            
        } catch (Exception $e) {
            error_log("Failed to create session notification logs table: " . $e->getMessage());
        }
    }
    
    /**
     * Get notification delivery rate for a specific type
     */
    public function getDeliveryRate($notificationType, $days = 30) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful,
                    ROUND((SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as success_rate
                FROM session_notification_logs
                WHERE notification_type = ? 
                AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                AND recipient_type != 'bulk'
            ");
            
            $stmt->execute([$notificationType, $days]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Failed to get delivery rate: " . $e->getMessage());
            return ['total' => 0, 'successful' => 0, 'success_rate' => 0];
        }
    }
    
    /**
     * Get notification trends over time
     */
    public function getNotificationTrends($days = 30) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT 
                    DATE(created_at) as date,
                    notification_type,
                    COUNT(*) as count,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful
                FROM session_notification_logs
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                AND recipient_type != 'bulk'
                GROUP BY DATE(created_at), notification_type
                ORDER BY date DESC, notification_type
            ");
            
            $stmt->execute([$days]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Failed to get notification trends: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get failed notifications for retry
     */
    public function getFailedNotifications($limit = 100) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT 
                    snl.*,
                    es.session_title,
                    m.email, m.first_name, m.last_name
                FROM session_notification_logs snl
                LEFT JOIN event_sessions es ON snl.session_id = es.id
                LEFT JOIN members m ON snl.recipient_id = m.id
                WHERE snl.success = 0 
                AND snl.recipient_type IN ('member', 'guest')
                ORDER BY snl.created_at DESC
                LIMIT ?
            ");
            
            $stmt->execute([$limit]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Failed to get failed notifications: " . $e->getMessage());
            return [];
        }
    }
}
?>
