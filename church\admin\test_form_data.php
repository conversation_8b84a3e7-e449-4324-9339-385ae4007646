<?php
session_start();
require_once '../config.php';

echo "<h1>🧪 Test Form Data Submission</h1>\n";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>📝 POST Data Received:</h2>\n";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr><th>Field</th><th>Value</th></tr>\n";
    
    foreach ($_POST as $key => $value) {
        if (is_array($value)) {
            $value = json_encode($value);
        }
        echo "<tr><td><strong>{$key}</strong></td><td>" . htmlspecialchars($value) . "</td></tr>\n";
    }
    echo "</table>\n";
    
    echo "<h2>🔍 Key Field Analysis:</h2>\n";
    echo "<ul>\n";
    echo "<li><strong>Status:</strong> " . ($_POST['status'] ?? 'NOT SET') . "</li>\n";
    echo "<li><strong>Is Recurring:</strong> " . (isset($_POST['is_recurring']) ? 'YES (value: ' . $_POST['is_recurring'] . ')' : 'NO') . "</li>\n";
    echo "<li><strong>Recurrence Type:</strong> " . ($_POST['recurrence_type'] ?? 'NOT SET') . "</li>\n";
    echo "<li><strong>Start DateTime:</strong> " . ($_POST['start_datetime'] ?? 'NOT SET') . "</li>\n";
    echo "<li><strong>End DateTime:</strong> " . ($_POST['end_datetime'] ?? 'NOT SET') . "</li>\n";
    echo "</ul>\n";
    
    echo "<h2>✅ Status Check:</h2>\n";
    $status = $_POST['status'] ?? 'not set';
    $isActive = ($status === 'published') ? 1 : 0;
    echo "<p><strong>Status Value:</strong> {$status}</p>\n";
    echo "<p><strong>Would be Active:</strong> " . ($isActive ? 'YES (1)' : 'NO (0)') . "</p>\n";
    
    echo "<h2>🔄 Recurring Check:</h2>\n";
    $isRecurring = isset($_POST['is_recurring']) && $_POST['is_recurring'] === 'on';
    echo "<p><strong>Is Recurring:</strong> " . ($isRecurring ? 'YES' : 'NO') . "</p>\n";
    if ($isRecurring) {
        echo "<p><strong>Recurrence Details:</strong></p>\n";
        echo "<ul>\n";
        echo "<li>Type: " . ($_POST['recurrence_type'] ?? 'NOT SET') . "</li>\n";
        echo "<li>Interval: " . ($_POST['recurrence_interval'] ?? 'NOT SET') . "</li>\n";
        echo "<li>Count: " . ($_POST['recurrence_count'] ?? 'NOT SET') . "</li>\n";
        echo "<li>End Date: " . ($_POST['recurrence_end_date'] ?? 'NOT SET') . "</li>\n";
        echo "</ul>\n";
    }
    
} else {
    echo "<p>No POST data received. Submit a form to test.</p>\n";
}

echo "<hr>\n";
echo "<h2>📋 Instructions:</h2>\n";
echo "<ol>\n";
echo "<li>Go to the events page</li>\n";
echo "<li>Create a new event with recurring settings and published status</li>\n";
echo "<li>Temporarily change the form action to point to this file</li>\n";
echo "<li>Submit the form to see what data is being sent</li>\n";
echo "</ol>\n";
?>
