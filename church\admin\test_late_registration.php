<?php
// Test late registration functionality
require_once '../config.php';
require_once '../includes/late_registration_helper.php';
require_once '../classes/EventStatusManager.php';

echo "<h2>Testing Late Registration Functionality</h2>";

// Initialize event status manager
$eventStatusManager = new EventStatusManager($pdo);

try {
    // Get all events to test
    $stmt = $pdo->query("
        SELECT id, title, event_date, status, allow_late_registration, 
               late_registration_cutoff_hours
        FROM events 
        ORDER BY event_date DESC 
        LIMIT 10
    ");
    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($events)) {
        echo "<p>No events found. Creating a test event...</p>";
        
        // Create a test event
        $stmt = $pdo->prepare("
            INSERT INTO events (title, description, event_date, location, created_by, status, allow_late_registration, late_registration_cutoff_hours)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $testEventData = [
            'Test Late Registration Event',
            'This is a test event for late registration functionality',
            date('Y-m-d H:i:s', strtotime('+2 hours')), // 2 hours from now
            'Test Location',
            1, // admin ID
            'published',
            1, // allow late registration
            1  // 1 hour cutoff
        ];
        
        $stmt->execute($testEventData);
        $testEventId = $pdo->lastInsertId();
        
        echo "<p>✅ Created test event with ID: $testEventId</p>";
        
        // Re-fetch events
        $stmt = $pdo->query("
            SELECT id, title, event_date, status, allow_late_registration, 
                   late_registration_cutoff_hours
            FROM events 
            ORDER BY event_date DESC 
            LIMIT 10
        ");
        $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    echo "<h3>Event Late Registration Status</h3>";
    echo "<table border='1' cellpadding='10' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>Event</th><th>Date</th><th>Status</th><th>Late Reg Allowed</th><th>Cutoff Hours</th><th>Registration Status</th><th>Message</th>";
    echo "</tr>";
    
    foreach ($events as $event) {
        $regStatus = checkLateRegistrationAllowed($pdo, $event['id']);
        
        echo "<tr>";
        echo "<td><strong>" . htmlspecialchars($event['title']) . "</strong><br><small>ID: " . $event['id'] . "</small></td>";
        echo "<td>" . date('M j, Y g:i A', strtotime($event['event_date'])) . "</td>";
        echo "<td><span style='padding: 2px 6px; border-radius: 3px; background-color: " . getStatusBgColor($event['status']) . "; color: white;'>" . ucfirst($event['status']) . "</span></td>";
        echo "<td>" . ($event['allow_late_registration'] ? '✅ Yes' : '❌ No') . "</td>";
        echo "<td>" . ($event['late_registration_cutoff_hours'] ?: 'None') . "</td>";
        echo "<td>" . ($regStatus['allowed'] ? '✅ Allowed' : '❌ Not Allowed') . "</td>";
        echo "<td>" . htmlspecialchars($regStatus['message']) . "<br><small>Reason: " . $regStatus['reason'] . "</small></td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Test specific scenarios
    echo "<h3>Testing Specific Scenarios</h3>";
    
    if (!empty($events)) {
        $testEvent = $events[0];
        echo "<h4>Testing with Event: " . htmlspecialchars($testEvent['title']) . "</h4>";
        
        // Test 1: Check current status
        echo "<h5>1. Current Registration Status</h5>";
        $status = checkLateRegistrationAllowed($pdo, $testEvent['id']);
        echo "<p><strong>Status:</strong> " . ($status['allowed'] ? 'Allowed' : 'Not Allowed') . "</p>";
        echo "<p><strong>Message:</strong> " . htmlspecialchars($status['message']) . "</p>";
        echo "<p><strong>Reason:</strong> " . $status['reason'] . "</p>";
        
        // Test 2: Get registration message
        echo "<h5>2. Registration Message HTML</h5>";
        $message = getLateRegistrationMessage($pdo, $testEvent['id']);
        echo $message;
        
        // Test 3: Get registration button
        echo "<h5>3. Registration Button</h5>";
        $button = getRegistrationButton($pdo, $testEvent['id']);
        echo $button;
        
        // Test 4: Check if user can register
        echo "<h5>4. User Registration Check (User ID: 1)</h5>";
        $userStatus = canUserRegister($pdo, $testEvent['id'], 1);
        echo "<p><strong>Can Register:</strong> " . ($userStatus['allowed'] ? 'Yes' : 'No') . "</p>";
        echo "<p><strong>Message:</strong> " . htmlspecialchars($userStatus['message']) . "</p>";
        
        // Test 5: Update late registration settings
        echo "<h5>5. Testing Late Registration Settings Update</h5>";
        $updateResult = $eventStatusManager->updateLateRegistrationSettings($testEvent['id'], true, 2);
        echo "<p><strong>Update Result:</strong> " . ($updateResult['success'] ? 'Success' : 'Failed') . "</p>";
        echo "<p><strong>Message:</strong> " . htmlspecialchars($updateResult['message']) . "</p>";
        
        if ($updateResult['success']) {
            // Check updated status
            $updatedStatus = checkLateRegistrationAllowed($pdo, $testEvent['id']);
            echo "<p><strong>Updated Status:</strong> " . ($updatedStatus['allowed'] ? 'Allowed' : 'Not Allowed') . "</p>";
            echo "<p><strong>Updated Message:</strong> " . htmlspecialchars($updatedStatus['message']) . "</p>";
        }
    }
    
    echo "<h3>✅ Late Registration Testing Complete!</h3>";
    
} catch (Exception $e) {
    echo "<h3>❌ Error during testing: " . htmlspecialchars($e->getMessage()) . "</h3>";
    error_log("Late registration test error: " . $e->getMessage());
}

function getStatusBgColor($status) {
    $colors = [
        'draft' => '#ffc107',
        'published' => '#28a745',
        'cancelled' => '#dc3545',
        'completed' => '#6c757d',
        'archived' => '#343a40'
    ];
    return $colors[$status] ?? '#6c757d';
}

echo "<hr>";
echo "<h3>Database Schema Verification</h3>";

try {
    // Check if new columns exist
    $stmt = $pdo->query("DESCRIBE events");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $required_columns = ['status', 'allow_late_registration', 'late_registration_cutoff_hours'];
    
    echo "<h4>Required Columns in Events Table:</h4>";
    foreach ($required_columns as $col) {
        $exists = false;
        foreach ($columns as $column) {
            if ($column['Field'] === $col) {
                $exists = true;
                break;
            }
        }
        echo "<p>" . ($exists ? "✅" : "❌") . " $col</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error checking schema: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
