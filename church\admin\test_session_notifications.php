<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Include notification manager
require_once '../classes/SessionNotificationManager.php';

// Database connection
$conn = $pdo;

$message = '';
$error = '';

// Test notification functionality
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_notifications'])) {
    try {
        // Initialize notification manager
        $sessionNotificationManager = new SessionNotificationManager($pdo);
        
        // Test if we can create the notification manager
        $message = "✅ SessionNotificationManager initialized successfully!";
        
        // Test if we can get admin recipients
        $admins = $sessionNotificationManager->getAdminRecipients();
        $message .= "<br>✅ Found " . count($admins) . " admin recipients.";
        
        // Test if we can get a test session
        $stmt = $conn->prepare("SELECT id, session_title FROM event_sessions LIMIT 1");
        $stmt->execute();
        $test_session = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($test_session) {
            $message .= "<br>✅ Found test session: " . htmlspecialchars($test_session['session_title']);
        } else {
            $message .= "<br>⚠️ No sessions found for testing.";
        }
        
    } catch (Exception $e) {
        $error = "❌ Error testing notifications: " . $e->getMessage();
    }
}

// Page title and header info
$page_title = 'Test Session Notifications';
$page_header = 'Test Session Notifications';
$page_description = 'Test the session notification functionality';

// Include header
include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-bell"></i> Test Session Notifications
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="events.php" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Events
        </a>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo $error; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Test Section -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-gear"></i> Notification System Test
        </h5>
    </div>
    <div class="card-body">
        <p>This page tests if the session notification system is properly integrated into the admin session attendance page.</p>
        
        <form method="POST">
            <button type="submit" name="test_notifications" class="btn btn-primary">
                <i class="bi bi-play-circle"></i> Test Notification System
            </button>
        </form>
        
        <hr>
        
        <h6>What this test checks:</h6>
        <ul>
            <li>✅ SessionNotificationManager can be initialized</li>
            <li>✅ Admin recipients can be retrieved</li>
            <li>✅ Test sessions exist in the database</li>
        </ul>
        
        <h6>Recent Changes Made:</h6>
        <ul>
            <li>✅ Added SessionNotificationManager include to session_attendance.php</li>
            <li>✅ Added notification calls after successful registration</li>
            <li>✅ Added search functionality for name, email, phone</li>
            <li>✅ Added pagination to session attendance page</li>
            <li>✅ Added phone column to attendance table</li>
        </ul>
        
        <div class="alert alert-info mt-3">
            <h6><i class="bi bi-info-circle"></i> How to test the full functionality:</h6>
            <ol>
                <li>Go to Events page and find an event with sessions</li>
                <li>Click on "Manage Sessions" for that event</li>
                <li>Click on "View Attendance" for a session</li>
                <li>Try adding a new registration - this should now send notifications to admins</li>
                <li>Test the search functionality by searching for attendee names, emails, or phone numbers</li>
                <li>Test pagination if there are many attendees</li>
            </ol>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
