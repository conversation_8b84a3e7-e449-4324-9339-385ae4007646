<?php
/**
 * User Event Sessions Interface
 * 
 * Allows authenticated users to view and register for event sessions
 */

session_start();

// Include configuration and classes
require_once '../config.php';
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';
require_once '../classes/SessionNotificationManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);
$sessionNotificationManager = new SessionNotificationManager($pdo);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];
$message = '';
$error = '';

// Get event ID from URL
$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

if ($event_id <= 0) {
    header("Location: events.php");
    exit();
}

// Get event details
try {
    $stmt = $pdo->prepare("
        SELECT e.*, 
               COUNT(er.id) as total_rsvps,
               COUNT(CASE WHEN er.status = 'attending' THEN 1 END) as attending_count
        FROM events e
        LEFT JOIN event_rsvps er ON e.id = er.event_id
        WHERE e.id = ?
        GROUP BY e.id
    ");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$event) {
        header("Location: events.php");
        exit();
    }
} catch (PDOException $e) {
    $error = "Error loading event: " . $e->getMessage();
    $event = null;
}

// Handle session registration
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'register_session') {
        $session_id = (int)$_POST['session_id'];
        
        try {
            // Check if user is already registered for this session
            $stmt = $pdo->prepare("
                SELECT id FROM session_attendance 
                WHERE session_id = ? AND member_id = ?
            ");
            $stmt->execute([$session_id, $userId]);
            
            if ($stmt->fetch()) {
                $error = "You are already registered for this session.";
            } else {
                // Check session capacity
                $stmt = $pdo->prepare("
                    SELECT es.max_attendees, COUNT(sa.id) as current_registrations
                    FROM event_sessions es
                    LEFT JOIN session_attendance sa ON es.id = sa.session_id
                    WHERE es.id = ?
                    GROUP BY es.id
                ");
                $stmt->execute([$session_id]);
                $session_info = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($session_info && $session_info['max_attendees'] && 
                    $session_info['current_registrations'] >= $session_info['max_attendees']) {
                    $error = "This session is full. Please try another session.";
                } else {
                    // Register user for session
                    $stmt = $pdo->prepare("
                        INSERT INTO session_attendance (session_id, member_id, attendance_status)
                        VALUES (?, ?, 'registered')
                    ");
                    $stmt->execute([$session_id, $userId]);

                    // Send registration confirmation email and notification
                    try {
                        $sessionNotificationManager->sendRegistrationConfirmation($session_id, $userId);
                        $sessionNotificationManager->notifyAdminNewRegistration($session_id, $userId);
                    } catch (Exception $e) {
                        error_log("Failed to send session registration notifications: " . $e->getMessage());
                        // Don't fail the registration if notifications fail
                    }

                    $message = "Successfully registered for the session!";

                    // Redirect to prevent form resubmission
                    header("Location: event_sessions.php?event_id=" . $event_id . "&registered=1");
                    exit();
                }
            }
        } catch (PDOException $e) {
            $error = "Error registering for session: " . $e->getMessage();
        }
    } elseif ($_POST['action'] === 'unregister_session') {
        $session_id = (int)$_POST['session_id'];
        
        try {
            $stmt = $pdo->prepare("
                DELETE FROM session_attendance 
                WHERE session_id = ? AND member_id = ?
            ");
            $stmt->execute([$session_id, $userId]);
            
            $message = "Successfully unregistered from the session.";
            
            // Redirect to prevent form resubmission
            header("Location: event_sessions.php?event_id=" . $event_id . "&unregistered=1");
            exit();
            
        } catch (PDOException $e) {
            $error = "Error unregistering from session: " . $e->getMessage();
        }
    }
}

// Check for success messages from redirect
if (isset($_GET['registered']) && $_GET['registered'] == '1') {
    $message = "Successfully registered for the session!";
} elseif (isset($_GET['unregistered']) && $_GET['unregistered'] == '1') {
    $message = "Successfully unregistered from the session.";
}

// Get sessions for this event with user registration status
$sessions = [];
try {
    $stmt = $pdo->prepare("
        SELECT es.*, 
               COUNT(sa.id) as registered_count,
               COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as attended_count,
               MAX(CASE WHEN sa.member_id = ? THEN sa.id END) as user_registration_id,
               MAX(CASE WHEN sa.member_id = ? THEN sa.attendance_status END) as user_status
        FROM event_sessions es
        LEFT JOIN session_attendance sa ON es.id = sa.session_id
        WHERE es.event_id = ? AND es.status = 'active'
        GROUP BY es.id
        ORDER BY es.start_datetime ASC
    ");
    $stmt->execute([$userId, $userId, $event_id]);
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error loading sessions: " . $e->getMessage());
}

// Get user's event RSVP status
$user_rsvp = null;
try {
    $stmt = $pdo->prepare("SELECT * FROM event_rsvps WHERE event_id = ? AND user_id = ?");
    $stmt->execute([$event_id, $userId]);
    $user_rsvp = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error loading user RSVP: " . $e->getMessage());
}

// Get site settings for branding
$sitename = get_organization_name() . ' - Event Sessions';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$site_name_result = $stmt->fetch(PDO::FETCH_ASSOC);
if ($site_name_result) {
    $sitename = $site_name_result['setting_value'] . ' - Event Sessions';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Sessions - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="../css/promotional-materials.css">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'includes/theme_css.php'; ?>

    <style>
        body {
            background-color: var(--bs-body-bg, #f8f9fa);
            color: var(--bs-body-color, #212529);
            font-family: var(--bs-font-sans-serif, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }

        .navbar {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            color: white !important;
            display: flex;
            align-items: center;
        }

        .navbar-logo {
            max-height: 40px;
            max-width: 150px;
            height: auto;
            width: auto;
            object-fit: contain;
        }

        .session-card {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }

        .session-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .session-details {
            font-size: 0.9rem;
        }

        .session-details i {
            width: 16px;
        }

        .border-success {
            border-color: #198754 !important;
            border-width: 2px !important;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>
?>

<div class="container mt-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">
            <i class="bi bi-collection"></i> Event Sessions
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="events.php" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Events
            </a>
            <?php if ($event): ?>
                <a href="event_detail.php?id=<?php echo $event['id']; ?>" class="btn btn-outline-info ms-2">
                    <i class="bi bi-info-circle"></i> Event Details
                </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($event): ?>
        <!-- Event Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-calendar-event"></i> Event Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h4><?php echo htmlspecialchars($event['title']); ?></h4>
                        <?php if (!empty($event['description'])): ?>
                            <p class="text-muted"><?php echo htmlspecialchars($event['description']); ?></p>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-4">
                        <p><strong>Date:</strong> <?php echo date('M j, Y', strtotime($event['event_date'])); ?></p>
                        <p><strong>Time:</strong> <?php echo date('g:i A', strtotime($event['event_date'])); ?></p>
                        <p><strong>Location:</strong> <?php echo htmlspecialchars($event['location'] ?: 'Not specified'); ?></p>
                        <p><strong>Capacity:</strong> <?php echo $event['max_attendees'] ? number_format($event['max_attendees']) : 'Unlimited'; ?></p>
                        
                        <!-- User's RSVP Status -->
                        <?php if ($user_rsvp): ?>
                            <div class="mt-3">
                                <span class="badge bg-<?php echo $user_rsvp['status'] === 'attending' ? 'success' : ($user_rsvp['status'] === 'maybe' ? 'warning' : 'secondary'); ?> px-3 py-2">
                                    <i class="bi bi-<?php echo $user_rsvp['status'] === 'attending' ? 'check-circle' : ($user_rsvp['status'] === 'maybe' ? 'question-circle' : 'x-circle'); ?> me-1"></i>
                                    RSVP: <?php echo ucfirst(str_replace('_', ' ', $user_rsvp['status'])); ?>
                                </span>
                            </div>
                        <?php else: ?>
                            <div class="mt-3">
                                <a href="enhanced_rsvp.php?event_id=<?php echo $event['id']; ?>" class="btn btn-primary btn-sm">
                                    <i class="bi bi-calendar-plus"></i> RSVP for Event
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sessions -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-collection"></i> Available Sessions
                </h5>
                <span class="badge bg-primary"><?php echo count($sessions); ?> Sessions</span>
            </div>
            <div class="card-body">
                <?php if (empty($sessions)): ?>
                    <div class="text-center py-5">
                        <i class="bi bi-collection display-1 text-muted"></i>
                        <h4 class="mt-3">No Sessions Available</h4>
                        <p class="text-muted">This event doesn't have any sessions yet.</p>
                        <p class="text-muted">Check back later or contact the organizers for more information.</p>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($sessions as $session): ?>
                            <?php
                            $is_past = strtotime($session['start_datetime']) < time();
                            $is_full = $session['max_attendees'] && $session['registered_count'] >= $session['max_attendees'];
                            $is_registered = !empty($session['user_registration_id']);
                            $can_register = !$is_past && !$is_full && !$is_registered;
                            ?>
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card h-100 session-card <?php echo $is_registered ? 'border-success' : ''; ?>">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0"><?php echo htmlspecialchars($session['session_title']); ?></h6>
                                        <?php if ($is_registered): ?>
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle"></i> Registered
                                            </span>
                                        <?php elseif ($is_full): ?>
                                            <span class="badge bg-warning">
                                                <i class="bi bi-exclamation-triangle"></i> Full
                                            </span>
                                        <?php elseif ($is_past): ?>
                                            <span class="badge bg-secondary">
                                                <i class="bi bi-clock-history"></i> Past
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-info">
                                                <i class="bi bi-calendar-plus"></i> Available
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="card-body">
                                        <?php if (!empty($session['session_description'])): ?>
                                            <p class="card-text text-muted small">
                                                <?php echo htmlspecialchars(substr($session['session_description'], 0, 100)); ?>
                                                <?php if (strlen($session['session_description']) > 100): ?>...<?php endif; ?>
                                            </p>
                                        <?php endif; ?>

                                        <div class="session-details">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="bi bi-calendar3 text-primary me-2"></i>
                                                <span><?php echo date('M j, Y', strtotime($session['start_datetime'])); ?></span>
                                            </div>
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="bi bi-clock text-primary me-2"></i>
                                                <span>
                                                    <?php echo date('g:i A', strtotime($session['start_datetime'])); ?> -
                                                    <?php echo date('g:i A', strtotime($session['end_datetime'])); ?>
                                                </span>
                                            </div>
                                            <?php if (!empty($session['location'])): ?>
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="bi bi-geo-alt text-primary me-2"></i>
                                                    <span><?php echo htmlspecialchars($session['location']); ?></span>
                                                </div>
                                            <?php endif; ?>
                                            <?php if (!empty($session['instructor_name'])): ?>
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="bi bi-person text-primary me-2"></i>
                                                    <span><?php echo htmlspecialchars($session['instructor_name']); ?></span>
                                                </div>
                                            <?php endif; ?>
                                            <div class="d-flex align-items-center mb-3">
                                                <i class="bi bi-people text-primary me-2"></i>
                                                <span>
                                                    <?php echo $session['registered_count']; ?>
                                                    <?php if ($session['max_attendees']): ?>
                                                        / <?php echo $session['max_attendees']; ?>
                                                    <?php endif; ?>
                                                    registered
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-footer">
                                        <?php if ($is_registered): ?>
                                            <div class="d-grid gap-2">
                                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="unregisterSession(<?php echo $session['id']; ?>, '<?php echo htmlspecialchars($session['session_title'], ENT_QUOTES); ?>')">
                                                    <i class="bi bi-x-circle"></i> Unregister
                                                </button>
                                                <small class="text-muted text-center">
                                                    Status: <?php echo ucfirst(str_replace('_', ' ', $session['user_status'])); ?>
                                                </small>
                                            </div>
                                        <?php elseif ($can_register): ?>
                                            <div class="d-grid">
                                                <button type="button" class="btn btn-primary btn-sm" onclick="registerSession(<?php echo $session['id']; ?>, '<?php echo htmlspecialchars($session['session_title'], ENT_QUOTES); ?>')">
                                                    <i class="bi bi-plus-circle"></i> Register
                                                </button>
                                            </div>
                                        <?php elseif ($is_full): ?>
                                            <div class="d-grid">
                                                <button class="btn btn-warning btn-sm" disabled>
                                                    <i class="bi bi-exclamation-triangle"></i> Session Full
                                                </button>
                                            </div>
                                        <?php elseif ($is_past): ?>
                                            <div class="d-grid">
                                                <button class="btn btn-secondary btn-sm" disabled>
                                                    <i class="bi bi-clock-history"></i> Session Ended
                                                </button>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- User's Registered Sessions Summary -->
        <?php
        $user_sessions = array_filter($sessions, function($session) {
            return !empty($session['user_registration_id']);
        });
        ?>

        <?php if (!empty($user_sessions)): ?>
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-person-check"></i> My Registered Sessions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Session</th>
                                    <th>Date & Time</th>
                                    <th>Location</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($user_sessions as $session): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($session['session_title']); ?></strong>
                                            <?php if (!empty($session['instructor_name'])): ?>
                                                <br><small class="text-muted">with <?php echo htmlspecialchars($session['instructor_name']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php echo date('M j, Y', strtotime($session['start_datetime'])); ?><br>
                                            <small class="text-muted"><?php echo date('g:i A', strtotime($session['start_datetime'])); ?> - <?php echo date('g:i A', strtotime($session['end_datetime'])); ?></small>
                                        </td>
                                        <td><?php echo htmlspecialchars($session['location'] ?: 'TBD'); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $session['user_status'] === 'attended' ? 'success' : 'info'; ?>">
                                                <?php echo ucfirst(str_replace('_', ' ', $session['user_status'])); ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>

    <?php else: ?>
        <div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle"></i> Event not found or could not be loaded.
        </div>
    <?php endif; ?>
</div>

<!-- Register Session Modal -->
<div class="modal fade" id="registerSessionModal" tabindex="-1" aria-labelledby="registerSessionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="registerSessionModalLabel">
                    <i class="bi bi-plus-circle"></i> Register for Session
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="registerSessionForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="register_session">
                    <input type="hidden" name="event_id" value="<?php echo htmlspecialchars($event_id); ?>">
                    <input type="hidden" name="session_id" id="register_session_id">

                    <p>Are you sure you want to register for the session "<strong id="register_session_title"></strong>"?</p>

                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>Registration Details:</strong>
                        <ul class="mb-0 mt-2">
                            <li>You will receive a confirmation of your registration</li>
                            <li>You can unregister at any time before the session starts</li>
                            <li>Please arrive on time for the session</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Register
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Unregister Session Modal -->
<div class="modal fade" id="unregisterSessionModal" tabindex="-1" aria-labelledby="unregisterSessionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="unregisterSessionModalLabel">
                    <i class="bi bi-exclamation-triangle text-warning"></i> Unregister from Session
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="unregisterSessionForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="unregister_session">
                    <input type="hidden" name="event_id" value="<?php echo htmlspecialchars($event_id); ?>">
                    <input type="hidden" name="session_id" id="unregister_session_id">

                    <p>Are you sure you want to unregister from the session "<strong id="unregister_session_title"></strong>"?</p>

                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>Please Note:</strong>
                        <ul class="mb-0 mt-2">
                            <li>This will remove your registration from the session</li>
                            <li>Your spot may be given to someone on the waitlist</li>
                            <li>You can register again if spots are available</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="bi bi-x-circle"></i> Unregister
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.session-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.session-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.session-details {
    font-size: 0.9rem;
}

.session-details i {
    width: 16px;
}

.border-success {
    border-color: #198754 !important;
    border-width: 2px !important;
}
</style>

<script>
function registerSession(sessionId, sessionTitle) {
    document.getElementById('register_session_id').value = sessionId;
    document.getElementById('register_session_title').textContent = sessionTitle;

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('registerSessionModal'));
    modal.show();
}

function unregisterSession(sessionId, sessionTitle) {
    document.getElementById('unregister_session_id').value = sessionId;
    document.getElementById('unregister_session_title').textContent = sessionTitle;

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('unregisterSessionModal'));
    modal.show();
}

// Auto-dismiss alerts after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert-dismissible');
        alerts.forEach(function(alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
});
</script>

<!-- Footer -->
<?php include 'includes/footer.php'; ?>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
