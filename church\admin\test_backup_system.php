<?php
// Test backup system functionality
require_once '../config.php';
require_once '../classes/DatabaseBackupManager.php';

echo "<h2>Testing Database Backup System</h2>";

$backupManager = new DatabaseBackupManager($pdo);

try {
    // Test 1: Create a test backup configuration
    echo "<h3>Test 1: Create Backup Configuration</h3>";
    
    $testConfig = [
        'name' => 'Test Backup Configuration',
        'description' => 'Test configuration for backup system',
        'backup_type' => 'full',
        'format' => 'json',
        'schedule_type' => 'manual',
        'retention_days' => 7,
        'is_active' => 1,
        'email_notifications' => 0
    ];
    
    $result = $backupManager->createBackupConfiguration($testConfig, 1);
    
    if ($result['success']) {
        echo "<p>✅ Configuration created successfully with ID: " . $result['id'] . "</p>";
        $testConfigId = $result['id'];
    } else {
        echo "<p>❌ Error creating configuration: " . htmlspecialchars($result['message']) . "</p>";
        $testConfigId = null;
    }
    
    // Test 2: Get all configurations
    echo "<h3>Test 2: Get All Backup Configurations</h3>";
    
    $configurations = $backupManager->getAllBackupConfigurations();
    echo "<p>Found " . count($configurations) . " backup configuration(s):</p>";
    
    if (!empty($configurations)) {
        echo "<table border='1' cellpadding='10' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th>ID</th><th>Name</th><th>Type</th><th>Format</th><th>Schedule</th><th>Active</th>";
        echo "</tr>";
        
        foreach ($configurations as $config) {
            echo "<tr>";
            echo "<td>" . $config['id'] . "</td>";
            echo "<td>" . htmlspecialchars($config['name']) . "</td>";
            echo "<td>" . ucfirst($config['backup_type']) . "</td>";
            echo "<td>" . strtoupper($config['format']) . "</td>";
            echo "<td>" . ucfirst($config['schedule_type']) . "</td>";
            echo "<td>" . ($config['is_active'] ? '✅ Yes' : '❌ No') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test 3: Create a manual backup
    echo "<h3>Test 3: Create Manual Backup</h3>";
    
    if ($testConfigId) {
        $backupResult = $backupManager->createBackup($testConfigId, true, 1);
        
        if ($backupResult['success']) {
            echo "<div style='color: green; padding: 10px; border: 1px solid green; background-color: #f0fff0;'>";
            echo "<strong>✅ Backup created successfully!</strong><br>";
            echo "<strong>Filename:</strong> " . htmlspecialchars($backupResult['filename']) . "<br>";
            echo "<strong>File Path:</strong> " . htmlspecialchars($backupResult['filepath']) . "<br>";
            echo "<strong>File Size:</strong> " . formatBytes($backupResult['size']) . "<br>";
            echo "</div>";
        } else {
            echo "<div style='color: red; padding: 10px; border: 1px solid red; background-color: #fff0f0;'>";
            echo "<strong>❌ Backup failed:</strong> " . htmlspecialchars($backupResult['message']);
            echo "</div>";
        }
    } else {
        echo "<p>⚠️ Skipping backup test - no test configuration available</p>";
    }
    
    // Test 4: Get backup history
    echo "<h3>Test 4: Get Backup History</h3>";
    
    $backupHistory = $backupManager->getBackupHistory(10);
    echo "<p>Found " . count($backupHistory) . " backup record(s):</p>";
    
    if (!empty($backupHistory)) {
        echo "<table border='1' cellpadding='10' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th>Backup Name</th><th>Configuration</th><th>Type</th><th>Format</th><th>Status</th><th>Size</th><th>Started</th>";
        echo "</tr>";
        
        foreach ($backupHistory as $backup) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($backup['backup_name']) . "</td>";
            echo "<td>" . htmlspecialchars($backup['config_name'] ?: 'Manual') . "</td>";
            echo "<td>" . ucfirst($backup['backup_type']) . "</td>";
            echo "<td>" . strtoupper($backup['format']) . "</td>";
            echo "<td>";
            $statusColors = [
                'completed' => 'green',
                'running' => 'blue',
                'failed' => 'red',
                'pending' => 'orange'
            ];
            $color = $statusColors[$backup['status']] ?? 'gray';
            echo "<span style='color: $color;'>" . ucfirst($backup['status']) . "</span>";
            echo "</td>";
            echo "<td>" . ($backup['file_size'] ? formatBytes($backup['file_size']) : '-') . "</td>";
            echo "<td>" . date('M j, Y g:i A', strtotime($backup['started_at'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test 5: Test different backup formats
    echo "<h3>Test 5: Test Different Backup Formats</h3>";
    
    $formats = ['sql', 'json'];
    $types = ['full', 'structure_only', 'data_only'];
    
    foreach ($formats as $format) {
        foreach ($types as $type) {
            echo "<h4>Testing: $format format, $type backup</h4>";
            
            $testResult = $backupManager->createBackup(null, true, 1);
            
            if ($testResult['success']) {
                echo "<p>✅ $format/$type backup successful - " . htmlspecialchars($testResult['filename']) . " (" . formatBytes($testResult['size']) . ")</p>";
            } else {
                echo "<p>❌ $format/$type backup failed: " . htmlspecialchars($testResult['message']) . "</p>";
            }
        }
    }
    
    // Test 6: Test scheduled backup detection
    echo "<h3>Test 6: Test Scheduled Backup Detection</h3>";
    
    $scheduledBackups = $backupManager->getScheduledBackups();
    echo "<p>Found " . count($scheduledBackups) . " scheduled backup(s) ready to run:</p>";
    
    if (!empty($scheduledBackups)) {
        foreach ($scheduledBackups as $scheduled) {
            echo "<p>- " . htmlspecialchars($scheduled['name']) . " (" . $scheduled['schedule_type'] . " at " . $scheduled['schedule_time'] . ")</p>";
        }
    } else {
        echo "<p>No scheduled backups ready to run at this time.</p>";
    }
    
    // Test 7: Cleanup test - delete test configuration
    echo "<h3>Test 7: Cleanup Test Configuration</h3>";
    
    if ($testConfigId) {
        $deleteResult = $backupManager->deleteBackupConfiguration($testConfigId);
        
        if ($deleteResult['success']) {
            echo "<p>✅ Test configuration deleted successfully</p>";
        } else {
            echo "<p>❌ Error deleting test configuration: " . htmlspecialchars($deleteResult['message']) . "</p>";
        }
    }
    
    echo "<h3>✅ Backup System Testing Complete!</h3>";
    
} catch (Exception $e) {
    echo "<h3>❌ Error during testing: " . htmlspecialchars($e->getMessage()) . "</h3>";
    error_log("Backup system test error: " . $e->getMessage());
}

echo "<hr>";
echo "<h3>Database Schema Verification</h3>";

try {
    // Check if backup tables exist
    $tables_to_check = ['backup_configurations', 'backup_history'];
    
    echo "<h4>Required Tables:</h4>";
    foreach ($tables_to_check as $table) {
        try {
            $stmt = $pdo->query("SELECT 1 FROM $table LIMIT 1");
            echo "<p>✅ $table</p>";
        } catch (PDOException $e) {
            echo "<p>❌ $table - " . $e->getMessage() . "</p>";
        }
    }
    
    // Check backup directory
    $backupDir = __DIR__ . '/../backups/';
    echo "<h4>Backup Directory:</h4>";
    if (is_dir($backupDir)) {
        echo "<p>✅ Backup directory exists: $backupDir</p>";
        if (is_writable($backupDir)) {
            echo "<p>✅ Backup directory is writable</p>";
        } else {
            echo "<p>⚠️ Backup directory is not writable</p>";
        }
    } else {
        echo "<p>❌ Backup directory does not exist: $backupDir</p>";
    }
    
    // Check for mysqldump
    echo "<h4>System Requirements:</h4>";
    $mysqldumpPath = shell_exec('which mysqldump 2>/dev/null');
    if ($mysqldumpPath) {
        echo "<p>✅ mysqldump found: " . trim($mysqldumpPath) . "</p>";
    } else {
        echo "<p>⚠️ mysqldump not found - will use PHP fallback method</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error checking schema: " . htmlspecialchars($e->getMessage()) . "</p>";
}

function formatBytes($size, $precision = 2) {
    if ($size == 0) return '0 B';
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    return round($size, $precision) . ' ' . $units[$i];
}
?>
