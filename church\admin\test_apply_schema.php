<?php
// Test script to apply enhanced event management schema
require_once '../config.php';

echo "<h2>Applying Enhanced Event Management Schema</h2>";

try {
    // Read and execute the enhanced event management SQL
    $sql_file = __DIR__ . '/sql/enhanced_event_management.sql';
    
    if (file_exists($sql_file)) {
        $sql_content = file_get_contents($sql_file);
        
        // Split SQL statements and execute them
        $statements = array_filter(array_map('trim', explode(';', $sql_content)));
        
        $executed = 0;
        $errors = [];
        
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^\s*--/', $statement)) {
                try {
                    $pdo->exec($statement);
                    $executed++;
                    echo "<p>✅ Executed statement " . ($executed) . "</p>";
                } catch (PDOException $e) {
                    // Log error but continue with other statements
                    $errors[] = "Statement error: " . $e->getMessage();
                    echo "<p>⚠️ Error in statement " . ($executed + 1) . ": " . $e->getMessage() . "</p>";
                    error_log("SQL Statement Error: " . $e->getMessage() . " | Statement: " . substr($statement, 0, 100));
                }
            }
        }
        
        if (empty($errors)) {
            echo "<h3>✅ Enhanced event management schema applied successfully! Executed $executed statements.</h3>";
        } else {
            echo "<h3>⚠️ Schema partially applied. Executed $executed statements with " . count($errors) . " errors.</h3>";
            foreach ($errors as $error) {
                echo "<p style='color: red;'>❌ $error</p>";
            }
        }
        
    } else {
        echo "<h3>❌ SQL file not found: $sql_file</h3>";
    }
    
} catch (Exception $e) {
    echo "<h3>❌ Error applying schema: " . $e->getMessage() . "</h3>";
    error_log("Schema application error: " . $e->getMessage());
}

// Check current schema status
echo "<hr><h3>Schema Status Check</h3>";
try {
    // Check if new columns exist
    $stmt = $pdo->query("DESCRIBE events");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $required_columns = [
        'status', 'allow_late_registration', 'late_registration_cutoff_hours',
        'cancellation_reason', 'cancelled_at', 'cancelled_by',
        'archived_at', 'archived_by', 'is_recurring', 'recurrence_type'
    ];
    
    echo "<h4>Events Table Columns:</h4>";
    foreach ($required_columns as $col) {
        $exists = in_array($col, $columns);
        echo "<p>" . ($exists ? "✅" : "❌") . " $col</p>";
    }
    
    // Check if new tables exist
    $tables_to_check = [
        'event_status_history',
        'recurring_event_instances', 
        'event_settings',
        'backup_configurations',
        'backup_history'
    ];
    
    echo "<h4>New Tables:</h4>";
    foreach ($tables_to_check as $table) {
        try {
            $stmt = $pdo->query("SELECT 1 FROM $table LIMIT 1");
            echo "<p>✅ $table</p>";
        } catch (PDOException $e) {
            echo "<p>❌ $table</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error checking schema status: " . $e->getMessage() . "</p>";
}
?>
