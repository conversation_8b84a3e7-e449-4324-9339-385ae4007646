<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Fix Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script>
        const ADMIN_URL = 'admin';
    </script>
</head>
<body>
    <div class="container mt-5">
        <h1>Session Fix Test</h1>
        <p>Admin ID: <?php echo $_SESSION['admin_id']; ?></p>
        <p>Admin Name: <?php echo $_SESSION['admin_name'] ?? 'Unknown'; ?></p>
        
        <div class="row">
            <div class="col-md-6">
                <h3>Test Session Validation</h3>
                <button id="testSession" class="btn btn-primary">Test Session</button>
                <div id="sessionResult" class="mt-3"></div>
            </div>
            
            <div class="col-md-6">
                <h3>Test Event Creation</h3>
                <form id="testEventForm">
                    <input type="hidden" name="action" value="create_event">
                    <div class="mb-3">
                        <label for="title" class="form-label">Event Title</label>
                        <input type="text" class="form-control" id="title" name="title" value="Test Event" required>
                    </div>
                    <div class="mb-3">
                        <label for="start_datetime" class="form-label">Start Date/Time</label>
                        <input type="datetime-local" class="form-control" id="start_datetime" name="start_datetime" 
                               value="<?php echo date('Y-m-d\TH:i', strtotime('+1 day')); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="end_datetime" class="form-label">End Date/Time</label>
                        <input type="datetime-local" class="form-control" id="end_datetime" name="end_datetime" 
                               value="<?php echo date('Y-m-d\TH:i', strtotime('+1 day +2 hours')); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="location" class="form-label">Location</label>
                        <input type="text" class="form-control" id="location" name="location" value="Test Location" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description">Test event description</textarea>
                    </div>
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-control" id="status" name="status">
                            <option value="draft">Draft</option>
                            <option value="published">Published</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-success">Create Test Event</button>
                </form>
                <div id="eventResult" class="mt-3"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Test session validation
        document.getElementById('testSession').addEventListener('click', function() {
            fetch('test_session.php', {
                method: 'POST',
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('sessionResult').innerHTML = 
                    '<div class="alert alert-' + (data.success ? 'success' : 'danger') + '">' +
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>' +
                    '</div>';
            })
            .catch(error => {
                document.getElementById('sessionResult').innerHTML = 
                    '<div class="alert alert-danger">Error: ' + error.message + '</div>';
            });
        });

        // Test event creation
        document.getElementById('testEventForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch('create_event_with_materials.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('eventResult').innerHTML = 
                    '<div class="alert alert-' + (data.success ? 'success' : 'danger') + '">' +
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>' +
                    '</div>';
            })
            .catch(error => {
                document.getElementById('eventResult').innerHTML = 
                    '<div class="alert alert-danger">Error: ' + error.message + '</div>';
            });
        });
    </script>
</body>
</html>
