<?php
require_once '../config.php';

echo "<h2>Cleaning up Recurring Event Duplicates</h2>\n";

try {
    $pdo->beginTransaction();
    
    // First, let's see what we have
    echo "<h3>Current Recurring Events Status:</h3>\n";
    
    $stmt = $pdo->query("
        SELECT 
            e.id,
            e.title,
            e.event_date,
            e.is_recurring,
            e.parent_event_id,
            COUNT(rei.instance_event_id) as instance_count
        FROM events e
        LEFT JOIN recurring_event_instances rei ON e.id = rei.parent_event_id
        WHERE e.is_recurring = 1 OR e.parent_event_id IS NOT NULL
        GROUP BY e.id
        ORDER BY e.title, e.event_date
    ");
    
    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr><th>ID</th><th>Title</th><th>Date</th><th>Is Recurring</th><th>Parent ID</th><th>Instance Count</th></tr>\n";
    
    foreach ($events as $event) {
        echo "<tr>";
        echo "<td>{$event['id']}</td>";
        echo "<td>{$event['title']}</td>";
        echo "<td>{$event['event_date']}</td>";
        echo "<td>{$event['is_recurring']}</td>";
        echo "<td>{$event['parent_event_id']}</td>";
        echo "<td>{$event['instance_count']}</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    // Find and remove duplicate instances (events that have parent_event_id)
    echo "<h3>Removing Duplicate Instance Events:</h3>\n";
    
    $stmt = $pdo->prepare("
        SELECT id, title, parent_event_id
        FROM events
        WHERE parent_event_id IS NOT NULL
    ");
    $stmt->execute();
    $duplicates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Found " . count($duplicates) . " duplicate instance events to remove:</p>\n";
    
    foreach ($duplicates as $duplicate) {
        echo "<p>Removing: ID {$duplicate['id']} - {$duplicate['title']} (child of {$duplicate['parent_event_id']})</p>\n";
        
        // First remove from recurring_event_instances table
        $stmt = $pdo->prepare("DELETE FROM recurring_event_instances WHERE instance_event_id = ?");
        $stmt->execute([$duplicate['id']]);

        // Remove any RSVPs for this duplicate event
        $stmt = $pdo->prepare("DELETE FROM event_rsvps WHERE event_id = ?");
        $stmt->execute([$duplicate['id']]);

        $stmt = $pdo->prepare("DELETE FROM event_rsvps_guests WHERE event_id = ?");
        $stmt->execute([$duplicate['id']]);

        // Remove the duplicate event itself
        $stmt = $pdo->prepare("DELETE FROM events WHERE id = ?");
        $stmt->execute([$duplicate['id']]);
    }
    
    // Clean up the recurring_event_instances table completely since we're changing the approach
    echo "<h3>Cleaning up recurring_event_instances table:</h3>\n";
    $stmt = $pdo->prepare("DELETE FROM recurring_event_instances");
    $stmt->execute();
    echo "<p>Cleared recurring_event_instances table</p>\n";

    // Reset parent_event_id for remaining events
    echo "<h3>Resetting parent_event_id for remaining events:</h3>\n";
    $stmt = $pdo->prepare("UPDATE events SET parent_event_id = NULL WHERE parent_event_id IS NOT NULL");
    $stmt->execute();
    echo "<p>Reset parent_event_id for all remaining events</p>\n";

    $pdo->commit();
    
    echo "<h3>Cleanup Complete!</h3>\n";
    echo "<p>All duplicate recurring event instances have been removed.</p>\n";
    echo "<p>Only master/parent events remain in the database.</p>\n";
    
    // Show final status
    echo "<h3>Final Status:</h3>\n";
    $stmt = $pdo->query("
        SELECT id, title, event_date, is_recurring, recurrence_type
        FROM events
        WHERE is_recurring = 1
        ORDER BY title, event_date
    ");
    
    $remaining = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr><th>ID</th><th>Title</th><th>Date</th><th>Recurrence Type</th></tr>\n";
    
    foreach ($remaining as $event) {
        echo "<tr>";
        echo "<td>{$event['id']}</td>";
        echo "<td>{$event['title']}</td>";
        echo "<td>{$event['event_date']}</td>";
        echo "<td>{$event['recurrence_type']}</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
} catch (Exception $e) {
    $pdo->rollback();
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>\n";
}
?>
