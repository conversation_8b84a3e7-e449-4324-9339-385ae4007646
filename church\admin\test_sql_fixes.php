<?php
// Test script to verify SQL fixes
require_once '../config.php';

echo "<h2>Testing SQL Fixes for Events Page</h2>";

try {
    // Test 1: Basic events query with table aliases
    echo "<h3>Test 1: Basic events query with table aliases</h3>";
    $sql = "SELECT e.id, e.title, e.description, e.location, e.is_active FROM events e LIMIT 5";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "✅ Basic query successful. Found " . count($events) . " events.<br>";
    
    // Test 2: Search query with table aliases
    echo "<h3>Test 2: Search query with table aliases</h3>";
    $search_term = '%test%';
    $sql = "SELECT e.id, e.title FROM events e WHERE (e.title LIKE ? OR e.description LIKE ? OR e.location LIKE ?) LIMIT 5";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$search_term, $search_term, $search_term]);
    $search_results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "✅ Search query successful. Found " . count($search_results) . " matching events.<br>";
    
    // Test 3: Status filter with is_active
    echo "<h3>Test 3: Status filter with is_active</h3>";
    $sql = "SELECT e.id, e.title, e.is_active FROM events e WHERE e.is_active = 1 LIMIT 5";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $active_events = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "✅ Status filter query successful. Found " . count($active_events) . " active events.<br>";
    
    // Test 4: Complex query with joins (like in events.php)
    echo "<h3>Test 4: Complex query with joins</h3>";
    $sql = "
        SELECT e.*,
               ec.name as category_name
        FROM events e
        LEFT JOIN event_categories ec ON e.category_id = ec.id
        WHERE e.title LIKE ?
        ORDER BY e.event_date DESC
        LIMIT 5
    ";
    $stmt = $pdo->prepare($sql);
    $stmt->execute(['%test%']);
    $complex_results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "✅ Complex join query successful. Found " . count($complex_results) . " events with categories.<br>";
    
    // Test 5: Count query for pagination
    echo "<h3>Test 5: Count query for pagination</h3>";
    $sql = "SELECT COUNT(*) FROM events e WHERE (e.title LIKE ? OR e.description LIKE ? OR e.location LIKE ?)";
    $stmt = $pdo->prepare($sql);
    $stmt->execute(['%test%', '%test%', '%test%']);
    $total_count = $stmt->fetchColumn();
    echo "✅ Count query successful. Total matching events: " . $total_count . "<br>";
    
    echo "<br><h3>🎉 All SQL tests passed! The events.php errors should be fixed.</h3>";
    
} catch (PDOException $e) {
    echo "<h3>❌ SQL Error: " . $e->getMessage() . "</h3>";
    echo "<p>Error Code: " . $e->getCode() . "</p>";
} catch (Exception $e) {
    echo "<h3>❌ General Error: " . $e->getMessage() . "</h3>";
}

echo "<br><hr>";
echo "<h3>Database Schema Check</h3>";

try {
    // Check events table structure
    $stmt = $pdo->query("DESCRIBE events");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h4>Events table columns:</h4>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li><strong>" . $column['Field'] . "</strong> - " . $column['Type'];
        if ($column['Key']) echo " (Key: " . $column['Key'] . ")";
        echo "</li>";
    }
    echo "</ul>";
    
    // Check if status column exists
    $has_status = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'status') {
            $has_status = true;
            break;
        }
    }
    
    if ($has_status) {
        echo "<p>✅ Events table has 'status' column</p>";
    } else {
        echo "<p>ℹ️ Events table uses 'is_active' instead of 'status' column (this is correct)</p>";
    }
    
} catch (PDOException $e) {
    echo "<p>❌ Error checking database schema: " . $e->getMessage() . "</p>";
}
?>
