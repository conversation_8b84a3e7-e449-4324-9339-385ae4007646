# Session Management System Analysis

## Overview
This document provides a comprehensive analysis of the church management application's session management system, including database relationships, current workflows, and the implementation plan for enhanced email and notification systems.

## Database Schema

### Core Session Tables

#### 1. `event_sessions`
```sql
CREATE TABLE event_sessions (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    event_id INT(11) NOT NULL,                    -- Links to events.id
    session_title VARCHAR(255) NOT NULL,
    session_description TEXT,
    start_datetime DATETIME NOT NULL,
    end_datetime DATETIME NOT NULL,
    max_attendees INT(11) DEFAULT NULL,
    location VARCHAR(255),
    instructor_name VARCHAR(255),
    status ENUM('active', 'cancelled', 'completed') DEFAULT 'active',
    created_by INT(11) DEFAULT 1,                 -- Links to admin who created
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_event_id (event_id),
    INDEX idx_start_datetime (start_datetime),
    INDEX idx_status (status),
    INDEX idx_created_by (created_by)
);
```

#### 2. `session_attendance`
```sql
CREATE TABLE session_attendance (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    session_id INT(11) NOT NULL,                  -- Links to event_sessions.id
    member_id INT(11) DEFAULT NULL,               -- Links to members.id (for registered users)
    guest_name VARCHAR(255) DEFAULT NULL,         -- For non-registered attendees
    guest_email VARCHAR(255) DEFAULT NULL,        -- For non-registered attendees
    attendance_status ENUM('registered', 'attended', 'no_show') DEFAULT 'registered',
    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    attendance_date TIMESTAMP NULL,
    notes TEXT,
    INDEX idx_session_id (session_id),
    INDEX idx_member_id (member_id),
    INDEX idx_attendance_status (attendance_status)
);
```

### Related Tables

#### 3. `events` (Parent table)
- Contains main event information
- Sessions are sub-events of main events
- Relationship: events.id → event_sessions.event_id

#### 4. `members` (User table)
- Contains user information
- Relationship: members.id → session_attendance.member_id

## Current Workflow Analysis

### Admin Workflow
1. **Session Creation** (`admin/event_sessions.php`)
   - Admin selects an existing event
   - Creates session with title, description, datetime, location, instructor
   - Sets maximum attendees (optional)
   - Session stored in `event_sessions` table
   - **Missing**: No notification sent to users about new session

2. **Session Management**
   - Admin can view all sessions for an event
   - Can see registration counts and attendee lists
   - **Missing**: No admin notifications for registrations/cancellations

### User Workflow
1. **Session Discovery** (`user/event_sessions.php`)
   - Users browse sessions for a specific event
   - View session details, capacity, registration status
   - See their own registration status

2. **Session Registration**
   - Users can register for sessions (if not full)
   - Registration creates record in `session_attendance`
   - **Missing**: No confirmation email sent
   - **Missing**: No in-app notification

3. **Session Management** (`user/my_sessions.php`)
   - Users view all their registered sessions
   - Can unregister from future sessions
   - **Missing**: No cancellation notifications

## Current Email Infrastructure

### Email System Components
1. **Email Queue** (`email_queue` table)
   - Queued email processing with status tracking
   - Priority levels and retry logic
   - Scheduled email support

2. **Email Templates** (`email_templates` table)
   - Template-based email system
   - Category-based organization
   - Placeholder support

3. **Email Settings** (`email_settings` table)
   - SMTP configuration
   - Sender information

4. **Processing** (`cron/process_email_queue.php`)
   - Automated email processing every 5 minutes
   - Error handling and retry logic

## Current Notification Infrastructure

### In-App Notifications
1. **User Notifications** (`notifications` table)
   - Rich notification system with types, priorities
   - Read/unread status tracking
   - Expiration support

2. **Admin Notifications** (`admin_notifications` table)
   - Separate notification system for administrators
   - Activity-based notifications

3. **Notification Preferences** (`notification_preferences` table)
   - User-configurable notification settings
   - Email, web, and SMS preferences by type

4. **Display System**
   - Navbar notification bell with unread count
   - Real-time notification updates
   - Notification management interface

## Identified Gaps

### Email Notifications
- ❌ No session registration confirmation emails
- ❌ No session creation notifications to users
- ❌ No session update/cancellation emails
- ❌ No session reminder emails (24 hours before)
- ❌ No admin notifications for user registrations

### In-App Notifications
- ❌ No session-related notification types
- ❌ No session registration confirmations
- ❌ No session reminders
- ❌ No admin alerts for capacity/registrations

### User Experience
- ❌ No notification preferences for sessions
- ❌ No confirmation feedback after registration
- ❌ No proactive session discovery notifications

## Implementation Requirements

### Phase 1: Email Templates
- Session registration confirmation template
- Session creation announcement template
- Session update/cancellation template
- Session reminder template (24h before)
- Admin notification templates

### Phase 2: Notification Classes
- SessionNotificationManager class
- Email trigger integration
- In-app notification creation
- Error handling and logging

### Phase 3: Integration Points
- Admin session creation workflow
- User registration workflow
- Session update/cancellation workflow
- Automated reminder system

### Phase 4: User Preferences
- Session notification preferences
- Integration with existing preference system
- Granular control (email vs in-app)

## Success Metrics
- Email delivery rates for session notifications
- User engagement with session notifications
- Reduction in missed sessions
- Admin efficiency in session management
- User satisfaction with notification system
