<?php
require_once 'config.php';

echo "<h1>📋 Events Table Structure</h1>\n";

// Get table structure
$stmt = $pdo->prepare("DESCRIBE events");
$stmt->execute();
$columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h2>Table Columns:</h2>\n";
echo "<table border='1' cellpadding='5'>\n";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";

foreach ($columns as $col) {
    echo "<tr>";
    echo "<td>{$col['Field']}</td>";
    echo "<td>{$col['Type']}</td>";
    echo "<td>{$col['Null']}</td>";
    echo "<td>{$col['Key']}</td>";
    echo "<td>{$col['Default']}</td>";
    echo "<td>{$col['Extra']}</td>";
    echo "</tr>\n";
}
echo "</table>\n";

// Check event 35 data
echo "<h2>Event 35 Data:</h2>\n";
$stmt = $pdo->prepare("SELECT * FROM events WHERE id = 35");
$stmt->execute();
$event = $stmt->fetch(PDO::FETCH_ASSOC);

if ($event) {
    echo "<table border='1' cellpadding='5'>\n";
    foreach ($event as $field => $value) {
        echo "<tr><td><strong>$field</strong></td><td>" . htmlspecialchars($value) . "</td></tr>\n";
    }
    echo "</table>\n";
} else {
    echo "<p>Event 35 not found</p>\n";
}

?>
