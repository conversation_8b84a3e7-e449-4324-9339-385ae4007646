<!DOCTYPE html>
<html>
<head>
    <title>Test Upload</title>
</head>
<body>
    <h1>Test Promotional Material Upload</h1>
    
    <form id="uploadForm" enctype="multipart/form-data">
        <div>
            <label>Event ID:</label>
            <input type="number" id="eventId" name="event_id" value="1" required>
        </div>
        <div>
            <label>File:</label>
            <input type="file" id="fileInput" name="promotional_file" accept="image/*,application/pdf" required>
        </div>
        <div>
            <label>
                <input type="checkbox" id="headerBanner" name="is_header_banner" value="1">
                Set as header banner
            </label>
        </div>
        <div>
            <button type="button" onclick="testUpload()">Test Upload</button>
        </div>
    </form>
    
    <div id="results"></div>
    
    <script>
    function testUpload() {
        const form = document.getElementById('uploadForm');
        const formData = new FormData();
        
        const eventId = document.getElementById('eventId').value;
        const fileInput = document.getElementById('fileInput');
        const headerBanner = document.getElementById('headerBanner').checked;
        
        if (!fileInput.files[0]) {
            alert('Please select a file');
            return;
        }
        
        formData.append('event_id', eventId);
        formData.append('promotional_file', fileInput.files[0]);
        formData.append('is_header_banner', headerBanner);
        
        console.log('Sending upload request...');
        console.log('Event ID:', eventId);
        console.log('File:', fileInput.files[0].name);
        console.log('Header banner:', headerBanner);
        
        fetch('upload_promotional_material.php', {
            method: 'POST',
            body: formData,
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('Response status:', response.status);
            return response.text().then(text => {
                console.log('Response text:', text);
                try {
                    return JSON.parse(text);
                } catch (e) {
                    console.error('JSON parse error:', e);
                    throw new Error('Invalid JSON response: ' + text.substring(0, 200));
                }
            });
        })
        .then(data => {
            console.log('Response data:', data);
            document.getElementById('results').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('results').innerHTML = '<div style="color: red;">Error: ' + error.message + '</div>';
        });
    }
    </script>
</body>
</html>
